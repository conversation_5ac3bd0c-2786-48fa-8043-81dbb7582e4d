# Cấu hình LM Studio đã gán cứng

Dự án đã được cấu hình để sử dụng URL và model name cố định cho LM Studio, giúp đơn giản hóa việc thiết lập và sử dụng.

## Cấu hình hiện tại

- **URL LM Studio**: `http://localhost:1234`
- **Model**: `lmstudio-community/Meta-Llama-3.1-8B-Instruct-GGUF`

## File đã được cập nhật

### 1. `server/services/lmStudioApi.ts`
- URL và model name đã được gán cứng trong class `LMStudioAPI`
- Các method `setModel()` và `updateBaseUrl()` vẫn tồn tại để tương thích nhưng không thực hiện thay đổi
- Constructor không còn nhận tham số URL

### 2. `init.sql`
- <PERSON>ữ liệu mẫu trong bảng `translation_settings` đã được cập nhật với model name mới

### 3. Database
- Bảng `translation_settings` đã được cập nhật với model name mới

## Cách thay đổi cấu hình (nếu cần)

### Thay đổi URL LM Studio
1. Mở file `server/services/lmStudioApi.ts`
2. Tìm dòng: `private baseUrl: string = 'http://localhost:1234';`
3. Thay đổi URL theo ý muốn
4. Khởi động lại server

### Thay đổi Model
1. Mở file `server/services/lmStudioApi.ts`
2. Tìm dòng: `private selectedModel: string = 'lmstudio-community/Meta-Llama-3.1-8B-Instruct-GGUF';`
3. Thay đổi tên model theo ý muốn
4. Cập nhật database:
   ```sql
   UPDATE translation_settings SET selected_model = 'tên-model-mới';
   ```
5. Khởi động lại server

## Lưu ý

- Giao diện web vẫn hiển thị các tùy chọn cấu hình URL và model, nhưng chúng sẽ không có tác dụng
- Các API endpoint liên quan đến cấu hình vẫn hoạt động bình thường để tương thích
- Để sử dụng model khác, bạn cần đảm bảo model đó đã được tải trong LM Studio

## Kiểm tra kết nối

1. Đảm bảo LM Studio đang chạy trên `http://localhost:1234`
2. Đảm bảo model `lmstudio-community/Meta-Llama-3.1-8B-Instruct-GGUF` đã được tải
3. Kiểm tra trạng thái kết nối trong giao diện web

## Khôi phục cấu hình động (nếu cần)

Nếu muốn quay lại cấu hình động như trước:

1. Khôi phục constructor trong `LMStudioAPI`:
   ```typescript
   constructor(baseUrl: string = 'http://localhost:1234') {
     this.baseUrl = baseUrl.replace(/\/$/, '');
   }
   ```

2. Khôi phục các method:
   ```typescript
   setModel(modelId: string): void {
     this.selectedModel = modelId;
   }
   
   updateBaseUrl(url: string): void {
     this.baseUrl = url.replace(/\/$/, '');
   }
   ```

3. Thay đổi khai báo thuộc tính:
   ```typescript
   private baseUrl: string;
   private selectedModel: string | null = null;
   ```