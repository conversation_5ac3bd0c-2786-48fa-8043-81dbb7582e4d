# Hướng dẫn thiết lập cơ sở dữ liệu PostgreSQL Local

## Yêu cầu
- Docker và Docker Compose
- Node.js và npm

## Các bước thiết lập

### 1. Khởi động PostgreSQL với Docker
```bash
# Khởi động PostgreSQL container
docker-compose up -d

# Kiểm tra container đang chạy
docker-compose ps
```

### 2. Cấu hình biến môi trường
Sao chép file `.env.local` và đặt tên là `.env` (hoặc sử dụng trực tiếp `.env.local`):
```bash
cp .env.local .env
```

Hoặc export biến môi trường:
```bash
export DATABASE_URL="postgresql://subtranslate_user:subtranslate_password@localhost:5432/subtranslate"
```

### 3. Chạy migration (nếu cầ<PERSON>)
```bash
# Push schema to database
npm run db:push
```

### 4. Khởi động ứng dụng
```bash
# Chạy ở chế độ development
npm run dev
```

## Cấu trúc cơ sở dữ liệu

### Bảng `subtitle_files`
- Lưu trữ thông tin file phụ đề
- Trạng thái: pending, translating, completed, paused, error
- Theo dõi tiến độ dịch

### Bảng `subtitle_segments` 
- Lưu trữ từng đoạn phụ đề
- Kết nối với `subtitle_files` qua `file_id`
- Chứa text gốc và text đã dịch
- Hỗ trợ review và chỉnh sửa

### Bảng `translation_settings`
- Cấu hình LM Studio API
- Thiết lập batch translation
- Custom prompts cho dịch thuật

## Dữ liệu mẫu
Cơ sở dữ liệu sẽ được khởi tạo với:
- 1 file phụ đề mẫu (sample_anime.srt)
- 3 segments phụ đề tiếng Nhật
- Cấu hình mặc định cho translation settings

## Kết nối cơ sở dữ liệu
```
Host: localhost
Port: 5432
Database: subtranslate
Username: subtranslate_user
Password: subtranslate_password
```

## Lệnh hữu ích

### Dừng và xóa container
```bash
docker-compose down
```

### Xóa dữ liệu và khởi tạo lại
```bash
docker-compose down -v
docker-compose up -d
```

### Kết nối trực tiếp đến PostgreSQL
```bash
docker exec -it subtranslate_postgres psql -U subtranslate_user -d subtranslate
```

### Backup dữ liệu
```bash
docker exec subtranslate_postgres pg_dump -U subtranslate_user subtranslate > backup.sql
```

### Restore dữ liệu
```bash
docker exec -i subtranslate_postgres psql -U subtranslate_user subtranslate < backup.sql
```

## Chuyển đổi giữa Local và Neon Database

Ứng dụng tự động phát hiện loại database dựa trên `DATABASE_URL`:
- Nếu URL chứa `neon.tech` → sử dụng Neon serverless
- Ngược lại → sử dụng PostgreSQL local

Để chuyển đổi, chỉ cần thay đổi `DATABASE_URL` trong file `.env`.