# Sửa Lỗi API Chọn Model LM Studio

## Vấn Đề
<PERSON>hi gọi API `POST /api/lmstudio/settings` với dữ liệu `{"selectedModel":"google/gemma-3-4b"}`, server trả về lỗi:
```json
{"message":"Invalid settings data","errors":["Required","Required","Required","Required","Required","Required","Required"]}
```

## Nguyên Nhân
API route sử dụng `insertTranslationSettingsSchema` yêu cầu tất cả các field bắt buộc, trong khi client chỉ muốn cập nhật một field duy nhất (`selectedModel`).

## Giải Pháp

### 1. Tạo Schema Mới cho Partial Update
Thêm `updateTranslationSettingsSchema` trong `shared/schema.ts`:
```typescript
export const updateTranslationSettingsSchema = z.object({
  selectedModel: z.string().optional(),
  lmStudioUrl: z.string().optional(),
  batchSize: z.number().int().min(1).max(10).optional(),
  delayBetweenBatches: z.number().int().min(0).max(5000).optional(),
  contextWindow: z.number().int().min(0).max(5).optional(),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().int().min(50).max(2000).optional(),
  systemPrompt: z.string().min(10).max(1000).optional(),
  contextPrompt: z.string().min(1).max(200).optional(),
});
```

### 2. Cập Nhật API Route
Sửa `server/routes.ts` để sử dụng schema mới:
```typescript
// Import schema mới
import { updateTranslationSettingsSchema } from "@shared/schema";

// Sử dụng trong route
app.post("/api/lmstudio/settings", async (req, res) => {
  const validatedSettings = updateTranslationSettingsSchema.parse(req.body);
  // ...
});
```

### 3. Storage Đã Hỗ Trợ Partial Update
Phương thức `updateTranslationSettings` trong storage đã hỗ trợ partial update:
```typescript
async updateTranslationSettings(settingsData: Partial<TranslationSettings>): Promise<TranslationSettings> {
  // Chỉ cập nhật các field được cung cấp
}
```

## Kết Quả
Sau khi sửa lỗi:
- API `POST /api/lmstudio/settings` với `{"selectedModel":"google/gemma-3-4b"}` hoạt động bình thường
- Trả về status 200 với dữ liệu settings đã cập nhật
- Client có thể cập nhật từng field riêng lẻ mà không cần gửi tất cả field

## Test API
```bash
curl 'http://localhost:3000/api/lmstudio/settings' \
  -H 'Content-Type: application/json' \
  --data-raw '{"selectedModel":"google/gemma-3-4b"}'
```

## Files Đã Thay Đổi
- `shared/schema.ts`: Thêm `updateTranslationSettingsSchema`
- `server/routes.ts`: Cập nhật import và sử dụng schema mới

## Lưu Ý
- Schema cũ `insertTranslationSettingsSchema` vẫn được giữ lại cho việc tạo settings mới
- Schema mới `updateTranslationSettingsSchema` dành cho việc cập nhật partial
- Tất cả field trong schema update đều là optional