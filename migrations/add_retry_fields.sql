-- Migration: Add retry mechanism fields to subtitle_segments table
-- Created: $(date)

ALTER TABLE subtitle_segments 
ADD COLUMN retry_count INTEGER NOT NULL DEFAULT 0,
ADD COLUMN max_retries INTEGER NOT NULL DEFAULT 3,
ADD COLUMN last_error TEXT,
ADD COLUMN next_retry_at TIMESTAMP;

-- Create index for efficient retry queries
CREATE INDEX idx_subtitle_segments_retry ON subtitle_segments(next_retry_at, status) WHERE status = 'error' AND retry_count < max_retries;

-- Update existing error segments to be retryable
UPDATE subtitle_segments 
SET retry_count = 0, max_retries = 3 
WHERE status = 'error' AND retry_count IS NULL;