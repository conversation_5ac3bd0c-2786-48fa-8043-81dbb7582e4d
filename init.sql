-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create subtitle_files table
CREATE TABLE IF NOT EXISTS subtitle_files (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
    file_name TEXT NOT NULL,
    original_content TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'translating', 'completed', 'paused', 'error')),
    total_segments INTEGER NOT NULL DEFAULT 0,
    completed_segments INTEGER NOT NULL DEFAULT 0,
    error_segments INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create subtitle_segments table
CREATE TABLE IF NOT EXISTS subtitle_segments (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
    file_id VARCHAR NOT NULL REFERENCES subtitle_files(id) ON DELETE CASCADE,
    segment_index INTEGER NOT NULL,
    start_time TEXT NOT NULL,
    end_time TEXT NOT NULL,
    original_text TEXT NOT NULL,
    translated_text TEXT,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'translating', 'completed', 'error', 'paused')),
    context TEXT,
    review_translated_text TEXT,
    review_prompt TEXT,
    reviewed_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create translation_settings table
CREATE TABLE IF NOT EXISTS translation_settings (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
    lm_studio_url TEXT NOT NULL DEFAULT 'http://localhost:1234',
    selected_model TEXT,
    is_connected BOOLEAN NOT NULL DEFAULT FALSE,
    last_checked TIMESTAMP,
    batch_size INTEGER NOT NULL DEFAULT 1,
    delay_between_batches INTEGER NOT NULL DEFAULT 100,
    context_window INTEGER NOT NULL DEFAULT 2,
    temperature REAL NOT NULL DEFAULT 0.3,
    max_tokens INTEGER NOT NULL DEFAULT 500,
    system_prompt TEXT NOT NULL DEFAULT 'You are a professional Japanese to Vietnamese translator. Translate the given Japanese subtitle text to natural Vietnamese. Keep the same tone and meaning. Only return the translated text without explanations.',
    context_prompt TEXT NOT NULL DEFAULT 'Context: {context}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Insert sample data
-- Insert default translation settings
INSERT INTO translation_settings (id, lm_studio_url, selected_model, is_connected, batch_size, delay_between_batches, context_window, temperature, max_tokens, system_prompt, context_prompt)
VALUES (
    gen_random_uuid(),
    'http://localhost:1234',
    'lmstudio-community/Meta-Llama-3.1-8B-Instruct-GGUF',
    FALSE,
    1,
    100,
    2,
    0.3,
    500,
    'You are a professional Japanese to Vietnamese translator. Translate the given Japanese subtitle text to natural Vietnamese. Keep the same tone and meaning. Only return the translated text without explanations.',
    'Context: {context}'
);

-- Insert sample subtitle file
INSERT INTO subtitle_files (id, file_name, original_content, status, total_segments, completed_segments, error_segments)
VALUES (
    gen_random_uuid(),
    'sample_anime.srt',
    '1\n00:00:01,000 --> 00:00:03,000\nこんにちは、世界！\n\n2\n00:00:04,000 --> 00:00:06,000\nアニメが大好きです。\n\n3\n00:00:07,000 --> 00:00:09,000\nありがとうございました。',
    'pending',
    3,
    0,
    0
);

-- Get the file ID for sample segments
DO $$
DECLARE
    sample_file_id VARCHAR;
BEGIN
    SELECT id INTO sample_file_id FROM subtitle_files WHERE file_name = 'sample_anime.srt' LIMIT 1;
    
    -- Insert sample subtitle segments
    INSERT INTO subtitle_segments (file_id, segment_index, start_time, end_time, original_text, status, context)
    VALUES 
        (sample_file_id, 1, '00:00:01,000', '00:00:03,000', 'こんにちは、世界！', 'pending', NULL),
        (sample_file_id, 2, '00:00:04,000', '00:00:06,000', 'アニメが大好きです。', 'pending', 'こんにちは、世界！'),
        (sample_file_id, 3, '00:00:07,000', '00:00:09,000', 'ありがとうございました。', 'pending', 'アニメが大好きです。');
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subtitle_segments_file_id ON subtitle_segments(file_id);
CREATE INDEX IF NOT EXISTS idx_subtitle_segments_status ON subtitle_segments(status);
CREATE INDEX IF NOT EXISTS idx_subtitle_files_status ON subtitle_files(status);