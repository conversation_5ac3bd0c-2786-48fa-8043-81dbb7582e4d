version: '3.8'
services:
  postgres:
    image: postgres:15
    container_name: subtranslate_postgres
    environment:
      POSTGRES_DB: subtranslate
      POSTGRES_USER: subtranslate_user
      POSTGRES_PASSWORD: subtranslate_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
      LC_ALL: C.UTF-8
      LANG: C.UTF-8
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

volumes:
  postgres_data: