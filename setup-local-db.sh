#!/bin/bash

# Script thiết lập cơ sở dữ liệu PostgreSQL local cho SubTranslate

echo "=== Thiết lập cơ sở dữ liệu PostgreSQL local ==="

# Kiểm tra PostgreSQL đã được cài đặt chưa
if ! command -v psql &> /dev/null && ! command -v /opt/homebrew/bin/psql &> /dev/null; then
    echo "PostgreSQL chưa được cài đặt. Vui lòng cài đặt PostgreSQL trước."
    echo "Trên macOS: brew install postgresql"
    echo "Trên Ubuntu: sudo apt-get install postgresql postgresql-contrib"
    exit 1
fi

# Thêm PostgreSQL vào PATH nếu cần
if command -v /opt/homebrew/bin/psql &> /dev/null; then
    export PATH="/opt/homebrew/bin:$PATH"
fi

# Kiểm tra PostgreSQL service đang chạy
if ! pg_isready -q; then
    echo "Khởi động PostgreSQL service..."
    # Trên macOS với Homebrew
    if command -v brew &> /dev/null; then
        brew services start postgresql
    else
        echo "Vui lòng khởi động PostgreSQL service thủ công"
        exit 1
    fi
fi

# Tạo database và user
echo "Tạo database và user..."
psql postgres -c "CREATE USER subtranslate_user WITH PASSWORD 'subtranslate_password';" 2>/dev/null || echo "User đã tồn tại"
psql postgres -c "CREATE DATABASE subtranslate OWNER subtranslate_user;" 2>/dev/null || echo "Database đã tồn tại"
psql postgres -c "GRANT ALL PRIVILEGES ON DATABASE subtranslate TO subtranslate_user;"

# Chạy init script
echo "Khởi tạo schema và dữ liệu mẫu..."
psql -U subtranslate_user -d subtranslate -f init.sql

echo "=== Thiết lập hoàn tất ==="
echo "Database URL: postgresql://subtranslate_user:subtranslate_password@localhost:5432/subtranslate"
echo "Để chạy ứng dụng:"
echo "export DATABASE_URL='postgresql://subtranslate_user:subtranslate_password@localhost:5432/subtranslate'"
echo "npm run dev"