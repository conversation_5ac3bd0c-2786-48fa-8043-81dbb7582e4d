import '../lm_studio_api_client.dart';
import '../../../domain/entities/srt/srt_entry.dart';
import 'dart:developer' as developer;

/// Service for translating SRT entries using LM Studio
class SRTTranslationService {
  final LMStudioApiClient _apiClient;

  SRTTranslationService({required LMStudioApiClient apiClient})
    : _apiClient = apiClient;

  /// Translates a single SRT entry
  Future<String> translateEntry({
    required SRTEntry entry,
    required String modelId,
    required String sourceLanguage,
    required String targetLanguage,
    String? previousLine,
    String? nextLine,
  }) async {
    developer.log('[SRTTranslationService] 🌐 Starting translation for entry ID: ${entry.id}');
    developer.log('[SRTTranslationService] 📝 Original content: "${entry.originalContent}"');
    developer.log('[SRTTranslationService] 🔧 Model: $modelId, $sourceLanguage → $targetLanguage');
    // Create system prompt using StringBuffer
    final StringBuffer systemPromptBuffer = StringBuffer()
      ..writeln(
        'You are a professional subtitle translator specializing in $sourceLanguage → $targetLanguage.',
      )
      ..writeln(
        'Context: The subtitles are from a Japanese Adult Video (JAV). Characters use informal, erotic, and sometimes vulgar language.',
      )
      ..writeln('Translation rules (strict):')
      ..writeln(
        '- Always prioritize semantic accuracy; do NOT paraphrase or generalize meaning.',
      )
      ..writeln(
        '- Do NOT invent subjects, pronouns, actors, genders, or objects not explicitly present in the source.',
      )
      ..writeln(
        '- If the original line has no explicit subject, produce a subjectless translation as well.',
      )
      ..writeln(
        '- Do NOT add explanations, comments, idioms, or filler phrases.',
      )
      ..writeln(
        '- Preserve erotic tone and explicit expressions WITHOUT censorship or softening.',
      )
      ..writeln(
        '- Avoid polite/formal Vietnamese expressions (e.g., "làm ơn", "xin", "vui lòng").',
      );

    final systemPrompt = systemPromptBuffer.toString().trim();

    // Create context-aware translation prompt
    final StringBuffer promptBuffer = StringBuffer();
    promptBuffer.writeln('Translate the following subtitle.');

    if (previousLine != null && previousLine.isNotEmpty) {
      promptBuffer.writeln('Previous line: $previousLine');
    }

    if (nextLine != null && nextLine.isNotEmpty) {
      promptBuffer.writeln('Next line: $nextLine');
    }

    promptBuffer.writeln('Target line: ${entry.originalContent}');
    promptBuffer.writeln();
    promptBuffer.writeln(
      'Use Previous line and Next line only as context to preserve tone and style.',
    );
    promptBuffer.writeln();
    promptBuffer.writeln(
      'Only output the Vietnamese translation for the target line.',
    );

    final userPrompt = promptBuffer.toString();

    // Send translation request to LM Studio
    developer.log('[SRTTranslationService] 📤 Sending request to LM Studio...');
    
    final response = await _apiClient.chatCompletion(
      modelId: modelId,
      messages: [
        {'role': 'system', 'content': systemPrompt},
        {'role': 'user', 'content': userPrompt},
      ],
      temperature: 0.0,
      maxTokens: 300,
    );
    
    developer.log('[SRTTranslationService] 📥 Received response from LM Studio');

    // Extract translated text from response
    final choices = response['choices'] as List;
    if (choices.isNotEmpty) {
      final message = choices[0]['message'] as Map<String, dynamic>;
      final translatedText = message['content'] as String;
      
      developer.log('[SRTTranslationService] ✅ Translation completed: "${translatedText.length > 100 ? translatedText.substring(0, 100) + '...' : translatedText}"');
      return translatedText;
    }

    final error = 'No translation returned from LM Studio';
    developer.log('[SRTTranslationService] ❌ $error');
    throw Exception(error);
  }

  /// Translates multiple SRT entries with context
  Future<Map<int, String>> translateEntries({
    required List<SRTEntry> entries,
    required String modelId,
    required String sourceLanguage,
    required String targetLanguage,
    List<SRTEntry>? allFileEntries,
  }) async {
    developer.log('[SRTTranslationService] 📊 Starting batch translation for ${entries.length} entries');
    
    final translations = <int, String>{};

    // If allFileEntries is provided, use it for context, otherwise use entries list
    final contextEntries = allFileEntries ?? entries;
    developer.log('[SRTTranslationService] 📋 Using ${contextEntries.length} entries for context');
    
    // Sort by index to ensure correct order
    contextEntries.sort((a, b) => a.index.compareTo(b.index));
    
    // Log context entries indices for debugging
    final contextIndices = contextEntries.map((e) => e.index).toList();
    developer.log('[SRTTranslationService] 🔢 Context entry indices: $contextIndices');

    for (final entry in entries) {
      try {
        // Find previous and next entries for context
        String? previousLine;
        String? nextLine;

        final currentIndex = contextEntries.indexWhere((e) => e.id == entry.id);
        developer.log('[SRTTranslationService] 🔍 Entry ${entry.id} (index ${entry.index}) found at position $currentIndex in context');
        
        if (currentIndex == -1) {
          developer.log('[SRTTranslationService] ⚠️ Entry ${entry.id} not found in context entries');
        } else {
          if (currentIndex > 0) {
            previousLine = contextEntries[currentIndex - 1].originalContent;
            developer.log('[SRTTranslationService] ⬅️ Previous context: "${previousLine?.substring(0, previousLine.length > 50 ? 50 : previousLine.length)}..."');
          }
          if (currentIndex < contextEntries.length - 1) {
            nextLine = contextEntries[currentIndex + 1].originalContent;
            developer.log('[SRTTranslationService] ➡️ Next context: "${nextLine?.substring(0, nextLine.length > 50 ? 50 : nextLine.length)}..."');
          }
        }

        final translatedText = await translateEntry(
          entry: entry,
          modelId: modelId,
          sourceLanguage: sourceLanguage,
          targetLanguage: targetLanguage,
          previousLine: previousLine,
          nextLine: nextLine,
        );
        translations[entry.id] = translatedText;
      } catch (e, stackTrace) {
        // If translation fails for one entry, continue with others
        developer.log(
          '[SRTTranslationService] ❌ Failed to translate entry ${entry.id}: $e',
          error: e,
          stackTrace: stackTrace,
        );
        
        // Log additional error details
        print('[SRTTranslationService] ❌ TRANSLATION ERROR DETAILS:');
        print('[SRTTranslationService] Entry ID: ${entry.id}');
        print('[SRTTranslationService] Entry Index: ${entry.index}');
        print('[SRTTranslationService] Error Type: ${e.runtimeType}');
        print('[SRTTranslationService] Error Message: $e');
        
        translations[entry.id] = ''; // Mark as failed
      }
    }

    return translations;
  }
}
