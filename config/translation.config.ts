/**
 * Translation Configuration
 * <PERSON><PERSON><PERSON> hình mặc định cho hệ thống dịch thuật
 */

export const TRANSLATION_CONFIG = {
  // LM Studio Connection
  lmStudioUrl: 'http://localhost:1234',
  selectedModel: 'hunyuan-mt-chimera-7b',
  
  // Batch Processing Settings
  batchSize: 1,
  delayBetweenBatches: 100, // milliseconds
  contextWindow: 2, // number of segments before/after for context
  
  // AI Model Parameters
  temperature: 0.3,
  maxTokens: 500,
  
  // Translation Prompts
  systemPrompt: 'You are a professional Japanese to Vietnamese translator. Translate the given Japanese subtitle text to natural Vietnamese. Keep the same tone and meaning. Only return the translated text without explanations.',
  contextPrompt: 'Context: {context}',
  
  // Connection Settings
  connectionTimeout: 5000, // milliseconds
  retryAttempts: 3,
  retryDelay: 1000, // milliseconds
} as const;

/**
 * Validation function for translation settings
 */
export function validateTranslationConfig(config: Partial<typeof TRANSLATION_CONFIG>) {
  const errors: string[] = [];
  
  if (config.batchSize && (config.batchSize < 1 || config.batchSize > 10)) {
    errors.push('Batch size must be between 1 and 10');
  }
  
  if (config.delayBetweenBatches && (config.delayBetweenBatches < 0 || config.delayBetweenBatches > 5000)) {
    errors.push('Delay between batches must be between 0 and 5000ms');
  }
  
  if (config.contextWindow && (config.contextWindow < 0 || config.contextWindow > 5)) {
    errors.push('Context window must be between 0 and 5');
  }
  
  if (config.temperature && (config.temperature < 0 || config.temperature > 1)) {
    errors.push('Temperature must be between 0 and 1');
  }
  
  if (config.maxTokens && (config.maxTokens < 50 || config.maxTokens > 2000)) {
    errors.push('Max tokens must be between 50 and 2000');
  }
  
  if (config.systemPrompt && (config.systemPrompt.length < 10 || config.systemPrompt.length > 1000)) {
    errors.push('System prompt must be between 10 and 1000 characters');
  }
  
  if (config.contextPrompt && (config.contextPrompt.length < 1 || config.contextPrompt.length > 200)) {
    errors.push('Context prompt must be between 1 and 200 characters');
  }
  
  return errors;
}

/**
 * Get merged configuration with defaults
 */
export function getTranslationConfig(overrides: Partial<typeof TRANSLATION_CONFIG> = {}) {
  return {
    ...TRANSLATION_CONFIG,
    ...overrides,
  };
}

export type TranslationConfigType = typeof TRANSLATION_CONFIG;