# Overview

This is a subtitle translation application built with a React frontend and Express backend that enables users to upload SRT subtitle files and translate them using LM Studio (a local LLM server). The application provides real-time translation monitoring, progress tracking, and translation management capabilities.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture
- **Framework**: React with TypeScript using Vite as the build tool
- **UI Library**: shadcn/ui components built on Radix UI primitives with Tailwind CSS styling
- **State Management**: TanStack Query for server state management and data fetching
- **Routing**: Wouter for lightweight client-side routing
- **Styling**: Tailwind CSS with CSS variables for theming and dark mode support

## Backend Architecture
- **Runtime**: Node.js with Express.js framework using TypeScript
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **File Processing**: Multer for handling file uploads with in-memory storage
- **API Design**: RESTful API endpoints for file management, translation control, and status monitoring

## Data Storage
- **Database Schema**: Three main tables - subtitle files, subtitle segments, and translation settings
- **File Storage**: SRT files are stored as text content in the database rather than the filesystem
- **Segment Management**: Each subtitle file is parsed into individual segments for granular translation tracking

## Translation System
- **Translation Engine**: Integration with LM Studio API for local LLM-based translation
- **Queue Management**: Custom translation queue system for managing concurrent translation jobs
- **Context Awareness**: Segments include context from adjacent subtitles for better translation accuracy
- **Status Tracking**: Real-time status updates for translation progress (pending, translating, completed, error, paused)

## Key Features
- **Real-time Updates**: Frontend polls backend every 1-2 seconds for live progress monitoring
- **Translation Controls**: Start, pause, resume, and stop translation operations
- **Error Handling**: Retry mechanism for failed segment translations
- **Export Functionality**: Generate and download translated SRT files
- **Connection Management**: Automatic LM Studio connection monitoring and model selection

# External Dependencies

## Database
- **Neon Database**: Serverless PostgreSQL database with connection pooling
- **Drizzle ORM**: Type-safe database operations with migration support

## Translation Service
- **LM Studio**: Local LLM server for translation processing
- **Connection**: Configurable LM Studio URL (default: http://localhost:1234)
- **Model Management**: Dynamic model selection from available LM Studio models

## UI Components
- **Radix UI**: Headless UI primitives for accessibility and functionality
- **Lucide React**: Icon library for consistent iconography
- **TanStack Query**: Data fetching and caching with real-time updates

## Development Tools
- **Vite**: Fast development server and build tool
- **TypeScript**: Type safety across frontend and backend
- **ESBuild**: Fast bundling for production builds
- **Replit Integration**: Development environment optimization for Replit platform