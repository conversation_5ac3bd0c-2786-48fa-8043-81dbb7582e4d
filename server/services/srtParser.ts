export interface SRTSegment {
  index: number;
  startTime: string;
  endTime: string;
  text: string;
}

export function parseSRT(srtContent: string): SRTSegment[] {
  const segments: SRTSegment[] = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    const lines = block.trim().split('\n');
    if (lines.length < 3) continue;

    const index = parseInt(lines[0]);
    const timecodeLine = lines[1];
    const textLines = lines.slice(2);

    // Parse timecode line (e.g., "00:00:12,500 --> 00:00:15,200")
    const timeMatch = timecodeLine.match(/(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})/);
    if (!timeMatch) continue;

    const startTime = timeMatch[1];
    const endTime = timeMatch[2];
    const text = textLines.join('\n').trim();

    segments.push({
      index,
      startTime,
      endTime,
      text
    });
  }

  return segments;
}

export function generateSRT(segments: Array<{ index: number; startTime: string; endTime: string; translatedText: string }>): string {
  return segments
    .map(segment => {
      return `${segment.index}\n${segment.startTime} --> ${segment.endTime}\n${segment.translatedText}\n`;
    })
    .join('\n');
}

export function createContext(segments: SRTSegment[], currentIndex: number): string {
  const context: string[] = [];
  
  // Add previous segment for context
  if (currentIndex > 0) {
    context.push(`Previous: "${segments[currentIndex - 1].text}"`);
  }
  
  // Add next segment for context
  if (currentIndex < segments.length - 1) {
    context.push(`Next: "${segments[currentIndex + 1].text}"`);
  }
  
  return context.length > 0 ? `Context: ${context.join(' | ')}` : '';
}
