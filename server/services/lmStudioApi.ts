import axios, { AxiosError } from 'axios';
import { TRANSLATION_CONFIG } from '../../config/translation.config';

export interface LMStudioModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

export interface TranslationRequest {
  text: string;
  context?: string;
  systemPrompt?: string;
  temperature?: number;
  maxTokens?: number;
}

export class LMStudioAPI {
  private baseUrl: string = TRANSLATION_CONFIG.lmStudioUrl;
  private selectedModel: string = TRANSLATION_CONFIG.selectedModel;

  constructor() {
    // Using configuration from translation.config.ts
  }

  async checkConnection(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/v1/models`, {
        timeout: TRANSLATION_CONFIG.connectionTimeout,
      });
      return response.status === 200;
    } catch (error) {
      console.error('LM Studio connection failed:', error);
      return false;
    }
  }

  async getModels(): Promise<LMStudioModel[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/v1/models`);
      return response.data.data || [];
    } catch (error) {
      console.error('Failed to get models:', error);
      throw new Error('Failed to fetch available models');
    }
  }

  setModel(modelId: string): void {
    // Model is configured, this method is kept for compatibility but does nothing
    console.log('Model is configured in translation.config.ts, ignoring setModel call');
  }

  async translateText(request: TranslationRequest): Promise<string> {
    // Validate input text - if only special characters or whitespace, return space
    const trimmedText = request.text.trim();
    if (!trimmedText || !/\w/.test(trimmedText)) {
      console.log('Text contains only special characters or whitespace, returning space');
      return ' ';
    }

    const systemPrompt = request.systemPrompt || TRANSLATION_CONFIG.systemPrompt;
    const prompt = this.buildTranslationPrompt(request.text, request.context);

    const requestPayload = {
      model: this.selectedModel,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      temperature: request.temperature || TRANSLATION_CONFIG.temperature,
      max_tokens: request.maxTokens || TRANSLATION_CONFIG.maxTokens,
      stream: false
    };

    // Log request details
    console.log('=== TRANSLATION API REQUEST ===');
    console.log('URL:', `${this.baseUrl}/v1/chat/completions`);
    console.log('Original Text:', request.text);
    console.log('Context:', request.context || 'None');
    console.log('System Prompt:', systemPrompt);
    console.log('User Prompt:', prompt);
    console.log('Request Payload:', JSON.stringify(requestPayload, null, 2));
    console.log('================================');

    try {
      const response = await axios.post(`${this.baseUrl}/v1/chat/completions`, requestPayload);

      // Log response details
      console.log('=== TRANSLATION API RESPONSE ===');
      console.log('Status:', response.status);
      console.log('Response Data:', JSON.stringify(response.data, null, 2));
      
      let translatedText = response.data.choices[0]?.message?.content?.trim();
      console.log('Raw Translated Text:', JSON.stringify(translatedText));
      
      if (!translatedText) {
        console.log('ERROR: Empty translation response');
        throw new Error('Empty translation response');
      }

      // Remove all quotes from the translated text
      const originalText = translatedText;
      translatedText = translatedText.replace(/"/g, '');
      if (originalText !== translatedText) {
        console.log('Removed all quotes, result:', JSON.stringify(translatedText));
      }

      console.log('Final Translated Text:', JSON.stringify(translatedText));
      console.log('=================================');

      return translatedText;
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error('Translation API error:', error.response?.data || error.message);
        throw new Error(`Translation failed: ${error.response?.data?.error?.message || error.message}`);
      }
      throw error;
    }
  }

  private buildTranslationPrompt(text: string, context?: string): string {
    let prompt = `Translate this Japanese subtitle text to Vietnamese:\n\n"${text}"`;
    
    if (context) {
      prompt += `\n\n${context}`;
    }
    
    prompt += '\n\nTranslation:';
    return prompt;
  }

  updateBaseUrl(url: string): void {
    // URL is configured, this method is kept for compatibility but does nothing
    console.log('URL is configured in translation.config.ts, ignoring updateBaseUrl call');
  }
}

export const lmStudioAPI = new LMStudioAPI();
