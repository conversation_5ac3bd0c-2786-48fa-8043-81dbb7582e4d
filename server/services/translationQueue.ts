import { storage } from '../storage';
import { lmStudioAPI } from './lmStudioApi';
import { createContext } from './srtParser';
import { TRANSLATION_CONFIG } from '../../config/translation.config';
import type { SubtitleSegment, TranslationSettings } from '@shared/schema';

export class TranslationQueue {
  private activeTranslations = new Map<string, boolean>(); // fileId -> isTranslating
  private pausedTranslations = new Set<string>(); // fileId set
  private retryTimeouts = new Map<string, NodeJS.Timeout>(); // segmentId -> timeout

  async startTranslation(fileId: string): Promise<void> {
    if (this.activeTranslations.get(fileId)) {
      throw new Error('Translation already in progress for this file');
    }

    this.activeTranslations.set(fileId, true);
    this.pausedTranslations.delete(fileId);

    try {
      await storage.updateSubtitleFileStatus(fileId, 'translating');
      await this.processFileTranslation(fileId);
    } catch (error) {
      console.error(`Translation failed for file ${fileId}:`, error);
      await storage.updateSubtitleFileStatus(fileId, 'error');
    } finally {
      this.activeTranslations.delete(fileId);
    }
  }

  async pauseTranslation(fileId: string): Promise<void> {
    this.pausedTranslations.add(fileId);
    await storage.updateSubtitleFileStatus(fileId, 'paused');
  }

  async stopTranslation(fileId: string): Promise<void> {
    this.activeTranslations.delete(fileId);
    this.pausedTranslations.delete(fileId);
    
    // Update all pending/translating segments to pending
    const segments = await storage.getSubtitleSegments(fileId);
    for (const segment of segments) {
      if (segment.status === 'translating') {
        await storage.updateSegmentStatus(segment.id, 'pending');
      }
    }
    
    await storage.updateSubtitleFileStatus(fileId, 'pending');
  }

  async resumeTranslation(fileId: string): Promise<void> {
    if (this.pausedTranslations.has(fileId)) {
      this.pausedTranslations.delete(fileId);
      await this.startTranslation(fileId);
    }
  }

  private async processFileTranslation(fileId: string): Promise<void> {
    const allSegments = await storage.getSubtitleSegments(fileId);
    
    // Use translation config instead of database settings
    const batchSize = TRANSLATION_CONFIG.batchSize;
    const delayBetweenBatches = TRANSLATION_CONFIG.delayBetweenBatches;
    const contextWindow = TRANSLATION_CONFIG.contextWindow;
    
    let batch: SubtitleSegment[] = [];
    
    while (!this.pausedTranslations.has(fileId) && this.activeTranslations.get(fileId)) {
      // Fill the batch with pending segments
      while (batch.length < batchSize && this.activeTranslations.get(fileId)) {
        const nextSegment = await storage.getNextPendingSegment(fileId);
        if (!nextSegment) break;
        batch.push(nextSegment);
      }
      
      // If no pending segments, check for retryable segments
      if (batch.length === 0) {
        const retryableSegments = await storage.getRetryableSegments(fileId);
        for (const segment of retryableSegments.slice(0, batchSize)) {
          batch.push(segment);
        }
      }
      
      if (batch.length === 0) {
        // No more pending or retryable segments, check if file is truly completed
        const currentSegments = await storage.getSubtitleSegments(fileId);
        const completedCount = currentSegments.filter(s => s.status === 'completed').length;
        const errorCount = currentSegments.filter(s => s.status === 'error' && s.retryCount >= s.maxRetries).length;
        const retryableCount = currentSegments.filter(s => s.status === 'error' && s.retryCount < s.maxRetries).length;
        
        if (retryableCount === 0) {
          // No more retryable segments, mark file as completed
          await storage.updateSubtitleFileStatus(fileId, 'completed', completedCount, errorCount);
          break;
        } else {
          // Still have retryable segments, wait a bit before checking again
          await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
          continue;
        }
      }

      // Process batch in parallel
      const batchPromises = batch.map(async (segment) => {
        if (this.pausedTranslations.has(fileId) || !this.activeTranslations.get(fileId)) {
          return;
        }

        try {
          // Mark segment as translating
          await storage.updateSegmentStatus(segment.id, 'translating');

          // Build context with configurable window
          const context = this.buildSegmentContext(allSegments, segment.segmentIndex, contextWindow, TRANSLATION_CONFIG.contextPrompt);

          // Translate with configurable settings
          const translatedText = await lmStudioAPI.translateText({
            text: segment.originalText,
            context,
            systemPrompt: TRANSLATION_CONFIG.systemPrompt,
            temperature: TRANSLATION_CONFIG.temperature,
            maxTokens: TRANSLATION_CONFIG.maxTokens
          });

          // Update segment with translation
          await storage.updateSegmentTranslation(segment.id, translatedText, 'completed');

        } catch (error) {
          console.error(`Failed to translate segment ${segment.id}:`, error);
          
          // Get current segment to check retry count
          const currentSegment = await storage.getSubtitleSegment(segment.id);
          if (currentSegment && currentSegment.retryCount < currentSegment.maxRetries) {
            // Schedule retry with exponential backoff
            await storage.updateSegmentError(segment.id, error instanceof Error ? error.message : String(error));
            this.scheduleRetry(currentSegment, fileId);
          } else {
            // Max retries reached or segment not found
            await storage.updateSegmentError(segment.id, `Max retries exceeded: ${error}`);
          }
        }
      });

      // Wait for batch to complete
      await Promise.all(batchPromises);

      // Update file progress after batch
      const currentSegments = await storage.getSubtitleSegments(fileId);
      const completedCount = currentSegments.filter(s => s.status === 'completed').length;
      const errorCount = currentSegments.filter(s => s.status === 'error' && s.retryCount >= s.maxRetries).length;
      await storage.updateSubtitleFileStatus(fileId, 'translating', completedCount, errorCount);

      // Clear batch for next iteration
      batch = [];

      // Configurable delay between batches
      if (delayBetweenBatches > 0) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
    }
  }

  private calculateRetryDelay(retryCount: number): number {
    // Exponential backoff: 2^retryCount * 1000ms (1s, 2s, 4s, 8s, ...)
    const baseDelay = 1000; // 1 second
    const maxDelay = 60000; // 1 minute max
    const delay = Math.min(baseDelay * Math.pow(2, retryCount), maxDelay);
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    return delay + jitter;
  }

  private scheduleRetry(segment: SubtitleSegment, fileId: string): void {
    const delay = this.calculateRetryDelay(segment.retryCount);
    const nextRetryAt = new Date(Date.now() + delay);
    
    // Clear existing timeout if any
    const existingTimeout = this.retryTimeouts.get(segment.id);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }
    
    // Schedule retry
    const timeout = setTimeout(async () => {
      try {
        await this.retrySegment(segment, fileId);
      } catch (error) {
        console.error(`Retry failed for segment ${segment.id}:`, error);
      } finally {
        this.retryTimeouts.delete(segment.id);
      }
    }, delay);
    
    this.retryTimeouts.set(segment.id, timeout);
    
    // Update database with next retry time
    storage.updateSegmentError(segment.id, segment.lastError || 'Unknown error', nextRetryAt);
  }

  private async retrySegment(segment: SubtitleSegment, fileId: string): Promise<void> {
    if (this.pausedTranslations.has(fileId) || !this.activeTranslations.get(fileId)) {
      return;
    }

    try {
      // Increment retry count
      await storage.incrementRetryCount(segment.id);
      
      // Get updated segment with new retry count
      const updatedSegment = await storage.getSubtitleSegment(segment.id);
      if (!updatedSegment) return;

      // Check if we've exceeded max retries
      if (updatedSegment.retryCount >= updatedSegment.maxRetries) {
        console.log(`Max retries exceeded for segment ${segment.id}`);
        return;
      }

      // Mark as translating and attempt translation
      await storage.updateSegmentStatus(segment.id, 'translating');
      
      // Get all segments for context
      const allSegments = await storage.getSubtitleSegments(fileId);
      const context = this.buildSegmentContext(allSegments, segment.segmentIndex, TRANSLATION_CONFIG.contextWindow, TRANSLATION_CONFIG.contextPrompt);

      // Attempt translation
      const translatedText = await lmStudioAPI.translateText({
        text: segment.originalText,
        context,
        systemPrompt: TRANSLATION_CONFIG.systemPrompt,
        temperature: TRANSLATION_CONFIG.temperature,
        maxTokens: TRANSLATION_CONFIG.maxTokens
      });

      // Success - update segment
      await storage.updateSegmentTranslation(segment.id, translatedText, 'completed');
      console.log(`Retry successful for segment ${segment.id} after ${updatedSegment.retryCount} attempts`);
      
    } catch (error) {
      console.error(`Retry attempt failed for segment ${segment.id}:`, error);
      
      // Get current segment state
      const currentSegment = await storage.getSubtitleSegment(segment.id);
      if (currentSegment && currentSegment.retryCount < currentSegment.maxRetries) {
        // Schedule another retry
        this.scheduleRetry(currentSegment, fileId);
      } else {
        // Max retries reached, mark as permanent error
        await storage.updateSegmentError(segment.id, `Max retries exceeded: ${error}`);
        console.log(`Segment ${segment.id} permanently failed after ${currentSegment?.retryCount || 0} retries`);
      }
    }
  }

  public buildSegmentContext(allSegments: SubtitleSegment[], currentIndex: number, contextWindow: number = 2, contextTemplate?: string): string {
    const context: string[] = [];
    
    // Add previous segments for context
    for (let i = 1; i <= contextWindow; i++) {
      const prevSegment = allSegments.find(s => s.segmentIndex === currentIndex - i);
      if (prevSegment) {
        context.unshift(`Previous ${i}: "${prevSegment.originalText}"`);
      }
    }
    
    // Add next segments for context
    for (let i = 1; i <= contextWindow; i++) {
      const nextSegment = allSegments.find(s => s.segmentIndex === currentIndex + i);
      if (nextSegment) {
        context.push(`Next ${i}: "${nextSegment.originalText}"`);
      }
    }
    
    if (context.length === 0) return '';
    
    const contextString = context.join(' | ');
    const template = contextTemplate || 'Context: {context}';
    return template.replace('{context}', contextString);
  }

  isTranslating(fileId: string): boolean {
    return this.activeTranslations.get(fileId) || false;
  }

  isPaused(fileId: string): boolean {
    return this.pausedTranslations.has(fileId);
  }
}

export const translationQueue = new TranslationQueue();
