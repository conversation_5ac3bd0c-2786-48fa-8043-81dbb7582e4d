import { 
  subtitleFiles, 
  subtitleSegments, 
  translationSettings,
  type SubtitleFile, 
  type SubtitleSegment,
  type TranslationSettings,
  type InsertSubtitleFile, 
  type InsertSubtitleSegment,
  type InsertTranslationSettings
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, asc, and, sql } from "drizzle-orm";

export interface IStorage {
  // Subtitle Files
  getSubtitleFiles(): Promise<SubtitleFile[]>;
  getSubtitleFile(id: string): Promise<SubtitleFile | undefined>;
  createSubtitleFile(file: InsertSubtitleFile): Promise<SubtitleFile>;
  updateSubtitleFileStatus(id: string, status: string, completedSegments?: number, errorSegments?: number): Promise<void>;
  deleteSubtitleFile(id: string): Promise<void>;

  // Subtitle Segments
  getSubtitleSegments(fileId: string): Promise<SubtitleSegment[]>;
  getSubtitleSegment(id: string): Promise<SubtitleSegment | undefined>;
  createSubtitleSegment(segment: InsertSubtitleSegment): Promise<SubtitleSegment>;
  updateSegmentTranslation(id: string, translatedText: string, status: 'pending' | 'translating' | 'completed' | 'error' | 'paused'): Promise<void>;
  updateSegmentStatus(id: string, status: 'pending' | 'translating' | 'completed' | 'error' | 'paused'): Promise<void>;
  getNextPendingSegment(fileId: string): Promise<SubtitleSegment | undefined>;
  
  // Retry mechanism
  updateSegmentError(id: string, error: string, nextRetryAt?: Date): Promise<void>;
  getRetryableSegments(fileId: string): Promise<SubtitleSegment[]>;
  incrementRetryCount(id: string): Promise<void>;
  resetSegmentRetry(id: string): Promise<void>;
  
  // Review Translation
  getSegmentById(id: string): Promise<SubtitleSegment | undefined>;
  updateSegmentReviewTranslation(id: string, reviewData: { reviewTranslatedText: string; reviewPrompt: string }): Promise<void>;
  acceptSegmentReview(id: string): Promise<SubtitleSegment>;

  // Translation Settings
  getTranslationSettings(): Promise<TranslationSettings | undefined>;
  updateTranslationSettings(settings: Partial<TranslationSettings>): Promise<TranslationSettings>;
}

export class DatabaseStorage implements IStorage {
  // Subtitle Files
  async getSubtitleFiles(): Promise<SubtitleFile[]> {
    const files = await db.select().from(subtitleFiles).orderBy(desc(subtitleFiles.createdAt));
    
    // Update each file with actual segment counts
    const updatedFiles = await Promise.all(files.map(async (file) => {
      const segments = await db.select().from(subtitleSegments).where(eq(subtitleSegments.fileId, file.id));
      const totalSegments = segments.length;
      const completedSegments = segments.filter(s => s.status === 'completed').length;
      

      return {
        ...file,
        totalSegments,
        completedSegments
      };
    }));
    
    return updatedFiles;
  }

  async getSubtitleFile(id: string): Promise<SubtitleFile | undefined> {
    const [file] = await db.select().from(subtitleFiles).where(eq(subtitleFiles.id, id));
    return file || undefined;
  }

  async createSubtitleFile(file: InsertSubtitleFile): Promise<SubtitleFile> {
    const [newFile] = await db
      .insert(subtitleFiles)
      .values(file)
      .returning();
    return newFile;
  }

  async updateSubtitleFileStatus(id: string, status: string, completedSegments?: number, errorSegments?: number): Promise<void> {
    const updates: any = { 
      status, 
      updatedAt: new Date() 
    };
    
    if (completedSegments !== undefined) {
      updates.completedSegments = completedSegments;
    }
    if (errorSegments !== undefined) {
      updates.errorSegments = errorSegments;
    }

    await db
      .update(subtitleFiles)
      .set(updates)
      .where(eq(subtitleFiles.id, id));
  }

  async deleteSubtitleFile(id: string): Promise<void> {
    await db.delete(subtitleFiles).where(eq(subtitleFiles.id, id));
  }

  // Subtitle Segments
  async getSubtitleSegments(fileId: string): Promise<SubtitleSegment[]> {
    return await db.select().from(subtitleSegments)
      .where(eq(subtitleSegments.fileId, fileId))
      .orderBy(asc(subtitleSegments.segmentIndex));
  }

  async getSubtitleSegment(id: string): Promise<SubtitleSegment | undefined> {
    const [segment] = await db.select().from(subtitleSegments).where(eq(subtitleSegments.id, id));
    return segment || undefined;
  }

  async createSubtitleSegment(segment: InsertSubtitleSegment): Promise<SubtitleSegment> {
    const [newSegment] = await db
      .insert(subtitleSegments)
      .values(segment)
      .returning();
    return newSegment;
  }

  async updateSegmentTranslation(id: string, translatedText: string, status: 'pending' | 'translating' | 'completed' | 'error' | 'paused'): Promise<void> {
    await db
      .update(subtitleSegments)
      .set({ 
        translatedText, 
        status, 
        updatedAt: new Date() 
      })
      .where(eq(subtitleSegments.id, id));
  }

  async updateSegmentStatus(id: string, status: 'pending' | 'translating' | 'completed' | 'error' | 'paused'): Promise<void> {
    await db
      .update(subtitleSegments)
      .set({ 
        status, 
        updatedAt: new Date() 
      })
      .where(eq(subtitleSegments.id, id));
  }

  async getNextPendingSegment(fileId: string): Promise<SubtitleSegment | undefined> {
    const [segment] = await db.select().from(subtitleSegments)
      .where(and(
        eq(subtitleSegments.fileId, fileId),
        eq(subtitleSegments.status, "pending")
      ))
      .orderBy(asc(subtitleSegments.segmentIndex))
      .limit(1);
    return segment || undefined;
  }

  // Retry mechanism methods
  async updateSegmentError(id: string, error: string, nextRetryAt?: Date): Promise<void> {
    await db
      .update(subtitleSegments)
      .set({ 
        status: 'error',
        lastError: error,
        nextRetryAt,
        updatedAt: new Date() 
      })
      .where(eq(subtitleSegments.id, id));
  }

  async getRetryableSegments(fileId: string): Promise<SubtitleSegment[]> {
    const now = new Date();
    return await db.select().from(subtitleSegments)
      .where(and(
        eq(subtitleSegments.fileId, fileId),
        eq(subtitleSegments.status, "error"),
        sql`${subtitleSegments.retryCount} < ${subtitleSegments.maxRetries}`,
        sql`${subtitleSegments.nextRetryAt} IS NULL OR ${subtitleSegments.nextRetryAt} <= ${now}`
      ))
      .orderBy(asc(subtitleSegments.segmentIndex));
  }

  async incrementRetryCount(id: string): Promise<void> {
    await db
      .update(subtitleSegments)
      .set({ 
        retryCount: sql`${subtitleSegments.retryCount} + 1`,
        updatedAt: new Date() 
      })
      .where(eq(subtitleSegments.id, id));
  }

  async resetSegmentRetry(id: string): Promise<void> {
    await db
      .update(subtitleSegments)
      .set({ 
        retryCount: 0,
        lastError: null,
        nextRetryAt: null,
        status: 'pending',
        updatedAt: new Date() 
      })
      .where(eq(subtitleSegments.id, id));
  }

  // Review Translation Methods
  async getSegmentById(id: string): Promise<SubtitleSegment | undefined> {
    const [segment] = await db.select().from(subtitleSegments).where(eq(subtitleSegments.id, id));
    return segment || undefined;
  }

  async updateSegmentReviewTranslation(id: string, reviewData: { reviewTranslatedText: string; reviewPrompt: string }): Promise<void> {
    await db
      .update(subtitleSegments)
      .set({
        reviewTranslatedText: reviewData.reviewTranslatedText,
        reviewPrompt: reviewData.reviewPrompt,
        reviewedAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(subtitleSegments.id, id));
  }

  async acceptSegmentReview(id: string): Promise<SubtitleSegment> {
    const segment = await this.getSegmentById(id);
    if (!segment?.reviewTranslatedText) {
      throw new Error("No review translation to accept");
    }

    const [updatedSegment] = await db
      .update(subtitleSegments)
      .set({
        translatedText: segment.reviewTranslatedText,
        updatedAt: new Date()
      })
      .where(eq(subtitleSegments.id, id))
      .returning();

    return updatedSegment;
  }

  // Translation Settings
  async getTranslationSettings(): Promise<TranslationSettings | undefined> {
    const [settings] = await db.select().from(translationSettings).limit(1);
    return settings || undefined;
  }

  async updateTranslationSettings(settingsData: Partial<TranslationSettings>): Promise<TranslationSettings> {
    const existing = await this.getTranslationSettings();
    
    if (existing) {
      const [updated] = await db
        .update(translationSettings)
        .set(settingsData)
        .where(eq(translationSettings.id, existing.id))
        .returning();
      return updated;
    } else {
      const [created] = await db
        .insert(translationSettings)
        .values(settingsData as InsertTranslationSettings)
        .returning();
      return created;
    }
  }
}

export const storage = new DatabaseStorage();
