import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import { drizzle as drizzleNode } from 'drizzle-orm/node-postgres';
import { Pool as NodePool } from 'pg';
import ws from "ws";
import * as schema from "@shared/schema";

if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?",
  );
}

// Check if using Neon database or local PostgreSQL
const isNeonDatabase = process.env.DATABASE_URL.includes('neon.tech');

let db: ReturnType<typeof drizzle> | ReturnType<typeof drizzleNode>;

if (isNeonDatabase) {
  // Use Neon serverless configuration
  neonConfig.webSocketConstructor = ws;
  const pool = new Pool({ connectionString: process.env.DATABASE_URL });
  db = drizzle({ client: pool, schema });
} else {
  // Use local PostgreSQL configuration
  const pool = new NodePool({ connectionString: process.env.DATABASE_URL });
  db = drizzleNode({ client: pool, schema });
}

export { db };
export const pool = isNeonDatabase 
  ? new Pool({ connectionString: process.env.DATABASE_URL })
  : new NodePool({ connectionString: process.env.DATABASE_URL });