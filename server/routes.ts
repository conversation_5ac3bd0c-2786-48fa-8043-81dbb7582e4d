import type { Express } from "express";
import { createServer, type Server } from "http";
import multer from "multer";
import { storage } from "./storage";
import { lmStudioAPI } from "./services/lmStudioApi";
import { translationQueue } from "./services/translationQueue";
import { parseSRT, generateSRT } from "./services/srtParser";
import { insertSubtitleFileSchema, insertTranslationSettingsSchema, updateTranslationSettingsSchema, reviewTranslateSchema } from "@shared/schema";
import { z } from "zod";

const updateSegmentTranslationSchema = z.object({
  translatedText: z.string().trim().min(1, "Translation text cannot be empty")
});

const upload = multer({ 
  storage: multer.memoryStorage(),
  fileFilter: (req, file, cb) => {
    // Ensure proper UTF-8 encoding for filename
    if (file.originalname) {
      file.originalname = Buffer.from(file.originalname, 'latin1').toString('utf8');
    }
    cb(null, true);
  }
});

export async function registerRoutes(app: Express): Promise<Server> {
  
  // Get all subtitle files
  app.get("/api/files", async (req, res) => {
    try {
      const files = await storage.getSubtitleFiles();
      res.json(files);
    } catch (error) {
      console.error("Failed to get files:", error);
      res.status(500).json({ message: "Failed to get files" });
    }
  });

  // Upload SRT file
  app.post("/api/files/upload", upload.single('file'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      const fileName = req.file.originalname;
      const fileContent = req.file.buffer.toString('utf-8');

      // Validate SRT content
      const segments = parseSRT(fileContent);
      if (segments.length === 0) {
        return res.status(400).json({ message: "Invalid SRT file format" });
      }

      // Create file record
      const file = await storage.createSubtitleFile({
        fileName,
        originalContent: fileContent
      });

      // Update total segments count
      await storage.updateSubtitleFileStatus(file.id, 'pending', 0, 0);

      // Create segment records
      for (const segment of segments) {
        await storage.createSubtitleSegment({
          fileId: file.id,
          segmentIndex: segment.index,
          startTime: segment.startTime,
          endTime: segment.endTime,
          originalText: segment.text
        });
      }

      // Update file with correct total segments
      await storage.updateSubtitleFileStatus(file.id, 'pending', 0, 0);

      res.json(file);
    } catch (error) {
      console.error("Failed to upload file:", error);
      res.status(500).json({ message: "Failed to upload file" });
    }
  });

  // Get segments for a file
  app.get("/api/files/:fileId/segments", async (req, res) => {
    try {
      const { fileId } = req.params;
      const segments = await storage.getSubtitleSegments(fileId);
      res.json(segments);
    } catch (error) {
      console.error("Failed to get segments:", error);
      res.status(500).json({ message: "Failed to get segments" });
    }
  });

  // Delete file
  app.delete("/api/files/:fileId", async (req, res) => {
    try {
      const { fileId } = req.params;
      
      // Stop translation if running
      await translationQueue.stopTranslation(fileId);
      
      await storage.deleteSubtitleFile(fileId);
      res.json({ success: true });
    } catch (error) {
      console.error("Failed to delete file:", error);
      res.status(500).json({ message: "Failed to delete file" });
    }
  });

  // Export translated SRT
  app.get("/api/files/:fileId/export", async (req, res) => {
    try {
      const { fileId } = req.params;
      const file = await storage.getSubtitleFile(fileId);
      const segments = await storage.getSubtitleSegments(fileId);

      if (!file) {
        return res.status(404).json({ message: "File not found" });
      }

      // Filter only completed segments
      const completedSegments = segments
        .filter(s => s.status === 'completed' && s.translatedText)
        .map(s => ({
          index: s.segmentIndex,
          startTime: s.startTime,
          endTime: s.endTime,
          translatedText: s.translatedText!
        }));

      if (completedSegments.length === 0) {
        return res.status(400).json({ message: "No translated segments available" });
      }

      const srtContent = generateSRT(completedSegments);
      const fileName = file.fileName.replace('.srt', '_vietnamese.srt');

      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.send(srtContent);
    } catch (error) {
      console.error("Failed to export file:", error);
      res.status(500).json({ message: "Failed to export file" });
    }
  });

  // Translation control
  app.post("/api/translation/:fileId/start", async (req, res) => {
    try {
      const { fileId } = req.params;
      await translationQueue.startTranslation(fileId);
      res.json({ success: true });
    } catch (error) {
      console.error("Failed to start translation:", error);
      res.status(500).json({ message: error instanceof Error ? error.message : "Failed to start translation" });
    }
  });

  app.post("/api/translation/:fileId/pause", async (req, res) => {
    try {
      const { fileId } = req.params;
      await translationQueue.pauseTranslation(fileId);
      res.json({ success: true });
    } catch (error) {
      console.error("Failed to pause translation:", error);
      res.status(500).json({ message: "Failed to pause translation" });
    }
  });

  app.post("/api/translation/:fileId/stop", async (req, res) => {
    try {
      const { fileId } = req.params;
      await translationQueue.stopTranslation(fileId);
      res.json({ success: true });
    } catch (error) {
      console.error("Failed to stop translation:", error);
      res.status(500).json({ message: "Failed to stop translation" });
    }
  });

  app.post("/api/translation/:fileId/resume", async (req, res) => {
    try {
      const { fileId } = req.params;
      await translationQueue.resumeTranslation(fileId);
      res.json({ success: true });
    } catch (error) {
      console.error("Failed to resume translation:", error);
      res.status(500).json({ message: "Failed to resume translation" });
    }
  });

  // LM Studio API routes
  app.get("/api/lmstudio/status", async (req, res) => {
    try {
      const isConnected = await lmStudioAPI.checkConnection();

      res.json({ 
        isConnected,
        url: 'http://localhost:1234',
        selectedModel: 'hunyuan-mt-chimera-7b'
      });
    } catch (error) {
      console.error("Failed to check LM Studio status:", error);
      res.status(500).json({ message: "Failed to check LM Studio status" });
    }
  });



  app.get("/api/lmstudio/models", async (req, res) => {
    try {
      const models = await lmStudioAPI.getModels();
      res.json(models);
    } catch (error) {
      console.error("Failed to get models:", error);
      res.status(500).json({ message: "Failed to get models" });
    }
  });



  // Review translate segment
  app.post("/api/segments/:segmentId/review-translate", async (req, res) => {
    try {
      const { segmentId } = req.params;
      const validatedBody = reviewTranslateSchema.parse(req.body);
      
      // Get segment and validate it exists and is completed
      const segment = await storage.getSegmentById(segmentId);
      if (!segment) {
        return res.status(404).json({ message: "Segment not found" });
      }
      
      if (segment.status !== 'completed') {
        return res.status(400).json({ message: "Can only review completed segments" });
      }
      
      // Get all segments for context building
      const allSegments = await storage.getSubtitleSegments(segment.fileId);
      
      // Get settings for context window and other defaults
      const settings = await storage.getTranslationSettings();
      const contextWindow = validatedBody.contextWindow ?? settings?.contextWindow ?? 2;
      
      // Build context using the same method as translation queue
      const context = translationQueue.buildSegmentContext(allSegments, segment.segmentIndex, contextWindow, settings?.contextPrompt);
      
      // Translate with review prompt
      const reviewTranslatedText = await lmStudioAPI.translateText({
        text: segment.originalText,
        context,
        systemPrompt: validatedBody.reviewPrompt,
        temperature: validatedBody.temperature ?? settings?.temperature,
        maxTokens: validatedBody.maxTokens ?? settings?.maxTokens
      });
      
      // Save review translation
      await storage.updateSegmentReviewTranslation(segmentId, {
        reviewTranslatedText,
        reviewPrompt: validatedBody.reviewPrompt
      });
      
      res.json({ reviewTranslatedText });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Invalid review data", 
          errors: error.errors.map(e => e.message) 
        });
      }
      console.error(`Failed to review translate segment:`, error);
      res.status(500).json({ message: "Failed to review translate segment" });
    }
  });

  // Accept review translation
  app.post("/api/segments/:segmentId/accept-review", async (req, res) => {
    try {
      const { segmentId } = req.params;
      
      // Get segment and validate review exists
      const segment = await storage.getSegmentById(segmentId);
      if (!segment) {
        return res.status(404).json({ message: "Segment not found" });
      }
      
      if (!segment.reviewTranslatedText) {
        return res.status(400).json({ message: "No review translation to accept" });
      }
      
      const updatedSegment = await storage.acceptSegmentReview(segmentId);
      res.json(updatedSegment);
    } catch (error) {
      console.error(`Failed to accept review translation:`, error);
      res.status(500).json({ message: "Failed to accept review translation" });
    }
  });

  // Retry failed segment
  app.post("/api/segments/:segmentId/retry", async (req, res) => {
    try {
      const { segmentId } = req.params;
      const segment = await storage.getSubtitleSegment(segmentId);
      
      if (!segment) {
        return res.status(404).json({ message: "Segment not found" });
      }

      await storage.updateSegmentStatus(segmentId, 'pending');
      res.json({ success: true });
    } catch (error) {
      console.error("Failed to retry segment:", error);
      res.status(500).json({ message: "Failed to retry segment" });
    }
  });

  // Update segment translation (for quality review)
  app.patch("/api/segments/:segmentId/translation", async (req, res) => {
    try {
      const { segmentId } = req.params;
      
      // Validate request body with Zod
      const validatedData = updateSegmentTranslationSchema.parse(req.body);
      const { translatedText } = validatedData;

      const segment = await storage.getSubtitleSegment(segmentId);
      if (!segment) {
        return res.status(404).json({ message: "Segment not found" });
      }

      // Only allow editing of completed segments to maintain workflow integrity
      if (segment.status !== 'completed') {
        return res.status(409).json({ 
          message: "Only completed translations can be edited",
          currentStatus: segment.status 
        });
      }

      // Preserve existing status (should be 'completed') instead of forcing it
      await storage.updateSegmentTranslation(segmentId, translatedText, segment.status);
      res.json({ success: true });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ 
          message: "Invalid request data", 
          errors: error.errors.map(e => e.message) 
        });
      }
      console.error("Failed to update segment translation:", error);
      res.status(500).json({ message: "Failed to update segment translation" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
