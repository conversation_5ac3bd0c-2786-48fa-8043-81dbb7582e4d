@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0 0% 96%);
  --foreground: hsl(0 0% 13%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 13%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 13%);
  --primary: hsl(207 90% 54%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(122 39% 49%);
  --secondary-foreground: hsl(0 0% 100%);
  --muted: hsl(0 0% 91%);
  --muted-foreground: hsl(0 0% 45%);
  --accent: hsl(207 90% 54%);
  --accent-foreground: hsl(0 0% 100%);
  --destructive: hsl(0 84% 60%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 89%);
  --input: hsl(0 0% 89%);
  --ring: hsl(207 90% 54%);
  --success: hsl(122 39% 49%);
  --warning: hsl(36 100% 50%);
  --chart-1: hsl(207 90% 54%);
  --chart-2: hsl(122 39% 49%);
  --chart-3: hsl(36 100% 50%);
  --chart-4: hsl(0 84% 60%);
  --chart-5: hsl(0 0% 45%);
  --sidebar: hsl(0 0% 100%);
  --sidebar-foreground: hsl(0 0% 13%);
  --sidebar-primary: hsl(207 90% 54%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(0 0% 91%);
  --sidebar-accent-foreground: hsl(207 90% 54%);
  --sidebar-border: hsl(0 0% 89%);
  --sidebar-ring: hsl(207 90% 54%);
  --font-sans: 'Inter', 'Noto Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-serif: 'Georgia', 'Times New Roman', serif;
  --font-mono: 'JetBrains Mono', 'Menlo', 'Monaco', 'Consolas', monospace;
  --radius: 8px;
}

.dark {
  --background: hsl(0 0% 0%);
  --foreground: hsl(0 0% 91%);
  --card: hsl(0 0% 10%);
  --card-foreground: hsl(0 0% 85%);
  --popover: hsl(0 0% 0%);
  --popover-foreground: hsl(0 0% 91%);
  --primary: hsl(207 90% 54%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(122 39% 49%);
  --secondary-foreground: hsl(0 0% 100%);
  --muted: hsl(0 0% 9%);
  --muted-foreground: hsl(0 0% 46%);
  --accent: hsl(207 90% 54%);
  --accent-foreground: hsl(0 0% 100%);
  --destructive: hsl(0 84% 60%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 15%);
  --input: hsl(0 0% 18%);
  --ring: hsl(207 90% 54%);
  --success: hsl(122 39% 49%);
  --warning: hsl(36 100% 50%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

.drag-over {
  border-color: var(--primary);
  background-color: hsl(207 90% 97%);
}

.progress-bar {
  transition: width 0.3s ease-in-out;
}

.translation-row:hover {
  background-color: hsl(0 0% 98%);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-pending { 
  background-color: hsl(0 0% 60%); 
}

.status-translating { 
  background-color: hsl(207 90% 54%); 
  animation: pulse 2s infinite; 
}

.status-completed { 
  background-color: hsl(122 39% 49%); 
}

.status-error { 
  background-color: hsl(0 84% 60%); 
}

.status-paused { 
  background-color: hsl(36 100% 50%); 
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
