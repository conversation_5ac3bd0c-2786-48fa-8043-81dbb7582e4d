import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { CheckCircle, XCircle, RefreshCw, Settings } from "lucide-react";

interface LMStudioStatus {
  isConnected: boolean;
  url: string;
  selectedModel?: string;
}

interface LMStudioModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

export function ConnectionStatus() {
  const [showSettings, setShowSettings] = useState(false);
  const [urlInput, setUrlInput] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch connection status
  const { data: status, isLoading: statusLoading } = useQuery<LMStudioStatus>({
    queryKey: ["/api/lmstudio/status"],
    refetchInterval: 5000, // Check every 5 seconds
  });

  // Fetch available models
  const { data: models = [], isLoading: modelsLoading } = useQuery<LMStudioModel[]>({
    queryKey: ["/api/lmstudio/models"],
    enabled: status?.isConnected || false,
    refetchInterval: 10000, // Refresh models every 10 seconds if connected
  });

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (settings: { lmStudioUrl?: string; selectedModel?: string }) => {
      const response = await apiRequest("POST", "/api/lmstudio/settings", settings);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Thành công",
        description: "Đã cập nhật cài đặt LM Studio",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/lmstudio/status"] });
      queryClient.invalidateQueries({ queryKey: ["/api/lmstudio/models"] });
      setShowSettings(false);
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleUpdateUrl = () => {
    if (urlInput.trim()) {
      updateSettingsMutation.mutate({ lmStudioUrl: urlInput.trim() });
    }
  };

  const handleModelSelect = (modelId: string) => {
    updateSettingsMutation.mutate({ selectedModel: modelId });
  };

  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ["/api/lmstudio/status"] });
    queryClient.invalidateQueries({ queryKey: ["/api/lmstudio/models"] });
  };

  return (
    <div className="p-4 border-b border-border bg-muted/30">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-foreground">LM Studio</span>
        <div className="flex items-center gap-2">
          <div className={`status-indicator ${
            statusLoading ? 'status-pending' :
            status?.isConnected ? 'status-completed' : 'status-error'
          }`} data-testid="indicator-connection-status" />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
            className="p-1 h-6 w-6"
            data-testid="button-toggle-settings"
          >
            <Settings className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={statusLoading}
            className="p-1 h-6 w-6"
            data-testid="button-refresh-status"
          >
            <RefreshCw className={`h-3 w-3 ${statusLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      <div className="text-xs text-muted-foreground mb-2" data-testid="text-lm-studio-url">
        {status?.url || 'localhost:1234'}
      </div>

      <div className="text-xs font-medium mb-2 flex items-center" data-testid="text-connection-message">
        {statusLoading ? (
          <>
            <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
            Đang kiểm tra...
          </>
        ) : status?.isConnected ? (
          <>
            <CheckCircle className="h-3 w-3 mr-1 text-success" />
            Đã kết nối
          </>
        ) : (
          <>
            <XCircle className="h-3 w-3 mr-1 text-destructive" />
            Không kết nối được
          </>
        )}
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="mb-3 p-3 border border-border rounded-lg bg-card">
          <div className="mb-2">
            <label className="text-xs font-medium text-foreground mb-1 block">URL LM Studio</label>
            <div className="flex gap-1">
              <Input
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                placeholder={status?.url || "http://localhost:1234"}
                className="text-xs h-7"
                data-testid="input-lm-studio-url"
              />
              <Button
                size="sm"
                onClick={handleUpdateUrl}
                disabled={!urlInput.trim() || updateSettingsMutation.isPending}
                className="h-7 px-2 text-xs"
                data-testid="button-update-url"
              >
                Cập nhật
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Model Selection */}
      {status?.isConnected && (
        <Select 
          value={status.selectedModel || ""} 
          onValueChange={handleModelSelect}
          disabled={modelsLoading || updateSettingsMutation.isPending}
        >
          <SelectTrigger className="w-full text-xs h-7" data-testid="select-model">
            <SelectValue placeholder={modelsLoading ? "Đang tải models..." : "Chọn model"} />
          </SelectTrigger>
          <SelectContent>
            {models.map((model) => (
              <SelectItem key={model.id} value={model.id} data-testid={`option-model-${model.id}`}>
                {model.id}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
    </div>
  );
}
