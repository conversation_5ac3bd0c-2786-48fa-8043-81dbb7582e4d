import { useState, useRef } from "react";
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { CloudUpload } from "lucide-react";

interface FileUploadProps {
  onFileUploaded: () => void;
}

export function FileUpload({ onFileUploaded }: FileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Upload failed');
      }
      
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Thành công",
        description: "File SRT đã được tải lên và phân tích",
      });
      onFileUploaded();
    },
    onError: (error) => {
      toast({
        title: "Lỗi tải file",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;
    
    Array.from(files).forEach(file => {
      if (!file.name.endsWith('.srt')) {
        toast({
          title: "Lỗi định dạng",
          description: "Chỉ hỗ trợ file SRT",
          variant: "destructive",
        });
        return;
      }
      uploadMutation.mutate(file);
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  };

  return (
    <div className="p-4 border-b border-border">
      <div 
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          isDragOver 
            ? 'border-primary bg-primary/5' 
            : 'border-border bg-muted/20 hover:border-primary'
        } ${uploadMutation.isPending ? 'opacity-50 cursor-not-allowed' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={!uploadMutation.isPending ? handleClick : undefined}
        data-testid="dropzone-file-upload"
      >
        <CloudUpload className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-sm font-medium text-foreground mb-1">
          {uploadMutation.isPending ? 'Đang tải lên...' : 'Kéo thả file SRT vào đây'}
        </p>
        <p className="text-xs text-muted-foreground">
          {uploadMutation.isPending ? 'Vui lòng đợi...' : 'hoặc nhấn để chọn file'}
        </p>
        <input 
          ref={fileInputRef}
          type="file" 
          accept=".srt" 
          multiple 
          className="hidden" 
          onChange={handleFileInputChange}
          disabled={uploadMutation.isPending}
          data-testid="input-file-upload"
        />
      </div>
    </div>
  );
}
