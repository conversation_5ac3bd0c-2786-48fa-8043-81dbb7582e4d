import type { SubtitleSegment, SubtitleFile } from "@shared/schema";

interface StatusFooterProps {
  segments: SubtitleSegment[];
  files: SubtitleFile[];
}

export function StatusFooter({ segments, files }: StatusFooterProps) {
  const totalSegments = segments.length;
  const completedSegments = segments.filter(s => s.status === 'completed').length;
  const pendingSegments = segments.filter(s => s.status === 'pending').length;
  const errorSegments = segments.filter(s => s.status === 'error').length;
  const translatingSegments = segments.filter(s => s.status === 'translating').length;
  
  // Retry statistics
  const retryableSegments = segments.filter(s => s.status === 'error' && s.retryCount < s.maxRetries).length;
  const maxRetriesReached = segments.filter(s => s.status === 'error' && s.retryCount >= s.maxRetries).length;

  const activeFiles = files.filter(f => f.status === 'translating' || f.status === 'paused').length;
  const isActive = activeFiles > 0 || translatingSegments > 0;

  // Simple estimation: assume 30 seconds per segment
  const remainingSegments = pendingSegments + translatingSegments;
  const estimatedMinutes = Math.ceil((remainingSegments * 30) / 60);

  return (
    <div className="bg-card border-t border-border px-6 py-3">
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center space-x-4 text-muted-foreground">
          <span data-testid="text-total-segments">
            Tổng cộng: {totalSegments} đoạn
          </span>
          <span data-testid="text-completed-segments">
            Đã dịch: {completedSegments}
          </span>
          <span data-testid="text-pending-segments">
            Chờ dịch: {pendingSegments}
          </span>
          {errorSegments > 0 && (
            <span className="text-destructive" data-testid="text-error-segments">
              Lỗi: {errorSegments}
              {retryableSegments > 0 && (
                <span className="text-orange-500 ml-1" data-testid="text-retryable-segments">
                  ({retryableSegments} có thể thử lại)
                </span>
              )}
            </span>
          )}
          {translatingSegments > 0 && (
            <span className="text-primary" data-testid="text-translating-segments">
              Đang dịch: {translatingSegments}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2 text-muted-foreground">
          {remainingSegments > 0 && (
            <span data-testid="text-estimated-time">
              Ước tính: {estimatedMinutes} phút còn lại
            </span>
          )}
          <div 
            className={`w-2 h-2 rounded-full ${
              isActive ? 'bg-success animate-pulse' : 'bg-muted'
            }`} 
            data-testid="indicator-activity"
          />
        </div>
      </div>
    </div>
  );
}
