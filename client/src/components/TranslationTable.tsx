import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { RotateCcw, Loader2, AlertTriangle, Edit2, Check, X, Eye, ThumbsUp, ThumbsDown } from "lucide-react";
import { useState } from "react";
import type { SubtitleSegment, SubtitleFile, ReviewTranslateRequest } from "@shared/schema";

interface TranslationTableProps {
  segments: SubtitleSegment[];
  isLoading: boolean;
  selectedFile: SubtitleFile | null;
}

export function TranslationTable({ segments, isLoading, selectedFile }: TranslationTableProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [editingSegment, setEditingSegment] = useState<string | null>(null);
  const [editText, setEditText] = useState("");
  
  // Review states
  const [reviewingSegment, setReviewingSegment] = useState<string | null>(null);
  const [reviewForm, setReviewForm] = useState<ReviewTranslateRequest>({
    reviewPrompt: "Bạn là một chuyên gia dịch thuật Nhật-Việt. Hãy xem xét và cải thiện bản dịch để trở nên tự nhiên và chính xác hơn. Giữ nguyên tông màu và ý nghĩa. Chỉ trả về văn bản đã dịch được cải thiện.",
    contextWindow: 2,
    temperature: 0.3,
    maxTokens: 500
  });
  const [reviewResult, setReviewResult] = useState<string | null>(null);

  const retryMutation = useMutation({
    mutationFn: async (segmentId: string) => {
      const response = await apiRequest("POST", `/api/segments/${segmentId}/retry`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/files"] });
      if (selectedFile) {
        queryClient.invalidateQueries({ queryKey: ["/api/files", selectedFile.id, "segments"] });
      }
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateTranslationMutation = useMutation({
    mutationFn: async ({ segmentId, translatedText }: { segmentId: string; translatedText: string }) => {
      const response = await apiRequest("PATCH", `/api/segments/${segmentId}/translation`, {
        body: JSON.stringify({ translatedText }),
        headers: { "Content-Type": "application/json" }
      });
      return response.json();
    },
    onSuccess: () => {
      setEditingSegment(null);
      setEditText("");
      queryClient.invalidateQueries({ queryKey: ["/api/files"] });
      if (selectedFile) {
        queryClient.invalidateQueries({ queryKey: ["/api/files", selectedFile.id, "segments"] });
      }
      toast({
        title: "Thành công",
        description: "Đã cập nhật bản dịch",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Review translation mutation
  const reviewTranslateMutation = useMutation({
    mutationFn: async ({ segmentId, reviewData }: { segmentId: string; reviewData: ReviewTranslateRequest }) => {
      const response = await apiRequest("POST", `/api/segments/${segmentId}/review-translate`, {
        body: JSON.stringify(reviewData),
        headers: { "Content-Type": "application/json" }
      });
      return response.json();
    },
    onSuccess: (data) => {
      setReviewResult(data.reviewTranslatedText);
      toast({
        title: "Thành công",
        description: "Đã tạo bản dịch review",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Accept review mutation
  const acceptReviewMutation = useMutation({
    mutationFn: async (segmentId: string) => {
      const response = await apiRequest("POST", `/api/segments/${segmentId}/accept-review`);
      return response.json();
    },
    onSuccess: () => {
      setReviewingSegment(null);
      setReviewResult(null);
      queryClient.invalidateQueries({ queryKey: ["/api/files"] });
      if (selectedFile) {
        queryClient.invalidateQueries({ queryKey: ["/api/files", selectedFile.id, "segments"] });
      }
      toast({
        title: "Thành công",
        description: "Đã áp dụng bản dịch review",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const startEditing = (segment: SubtitleSegment) => {
    setEditingSegment(segment.id);
    setEditText(segment.translatedText || "");
  };

  const cancelEditing = () => {
    setEditingSegment(null);
    setEditText("");
  };

  // Review helper functions
  const startReview = (segment: SubtitleSegment) => {
    setReviewingSegment(segment.id);
    setReviewResult(null);
  };

  const cancelReview = () => {
    setReviewingSegment(null);
    setReviewResult(null);
  };

  const submitReview = () => {
    if (reviewingSegment) {
      reviewTranslateMutation.mutate({
        segmentId: reviewingSegment,
        reviewData: reviewForm
      });
    }
  };

  const applyReview = () => {
    if (reviewingSegment) {
      acceptReviewMutation.mutate(reviewingSegment);
    }
  };

  const saveTranslation = (segmentId: string) => {
    if (editText.trim()) {
      updateTranslationMutation.mutate({ segmentId, translatedText: editText.trim() });
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 overflow-y-auto flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-primary" />
          <p className="text-muted-foreground">Đang tải segments...</p>
        </div>
      </div>
    );
  }

  if (!selectedFile) {
    return (
      <div className="flex-1 overflow-y-auto flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground text-lg mb-2">Chọn file để xem chi tiết</p>
          <p className="text-muted-foreground text-sm">Nhấn vào file bên trái để bắt đầu</p>
        </div>
      </div>
    );
  }

  if (segments.length === 0) {
    return (
      <div className="flex-1 overflow-y-auto flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground text-lg mb-2">File chưa có segments</p>
          <p className="text-muted-foreground text-sm">File có thể đang được xử lý</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="p-6">
        {/* Table Header */}
        <div className="bg-muted/50 rounded-t-lg border border-border">
          <div className="grid grid-cols-12 gap-4 px-4 py-3 text-xs font-semibold text-muted-foreground uppercase tracking-wide">
            <div className="col-span-1">STT</div>
            <div className="col-span-2">Thời gian</div>
            <div className="col-span-4">Tiếng Nhật (Gốc)</div>
            <div className="col-span-4">Tiếng Việt (Dịch)</div>
            <div className="col-span-1 text-center">Trạng thái</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="bg-card border-x border-b border-border">
          {segments.map((segment) => (
            <div 
              key={segment.id} 
              className="grid grid-cols-12 gap-4 px-4 py-3 border-b border-border translation-row hover:bg-muted/20 transition-colors"
              data-testid={`row-segment-${segment.id}`}
            >
              {/* Index */}
              <div className="col-span-1 text-sm text-muted-foreground font-medium" data-testid={`text-index-${segment.id}`}>
                {segment.segmentIndex}
              </div>

              {/* Timestamp */}
              <div className="col-span-2 text-sm text-muted-foreground font-mono" data-testid={`text-timestamp-${segment.id}`}>
                <div>{segment.startTime}</div>
                <div>--&gt;</div>
                <div>{segment.endTime}</div>
              </div>

              {/* Original Text */}
              <div className="col-span-4 text-sm text-foreground leading-relaxed" data-testid={`text-original-${segment.id}`}>
                {segment.originalText.split('\n').map((line, index) => (
                  <div key={index}>{line}</div>
                ))}
              </div>

              {/* Translation */}
              <div className="col-span-4 text-sm text-foreground leading-relaxed" data-testid={`text-translation-${segment.id}`}>
                {segment.status === 'completed' && segment.translatedText && (
                  <div className="bg-accent/10 border-l-4 border-accent pl-3 py-1 rounded-r relative group">
                    {editingSegment === segment.id ? (
                      <div className="space-y-2">
                        <Textarea
                          value={editText}
                          onChange={(e) => setEditText(e.target.value)}
                          className="min-h-[80px] text-sm resize-none"
                          placeholder="Nhập bản dịch..."
                          data-testid={`textarea-edit-${segment.id}`}
                        />
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => saveTranslation(segment.id)}
                            disabled={updateTranslationMutation.isPending || !editText.trim()}
                            className="h-7 px-3"
                            data-testid={`button-save-${segment.id}`}
                          >
                            {updateTranslationMutation.isPending ? (
                              <Loader2 className="h-3 w-3 animate-spin mr-1" />
                            ) : (
                              <Check className="h-3 w-3 mr-1" />
                            )}
                            Lưu
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={cancelEditing}
                            disabled={updateTranslationMutation.isPending}
                            className="h-7 px-3"
                            data-testid={`button-cancel-${segment.id}`}
                          >
                            <X className="h-3 w-3 mr-1" />
                            Hủy
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          {segment.translatedText.split('\n').map((line, index) => (
                            <div key={index}>{line}</div>
                          ))}
                        </div>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => startEditing(segment)}
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                            data-testid={`button-edit-${segment.id}`}
                          >
                            <Edit2 className="h-3 w-3" />
                          </Button>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => startReview(segment)}
                                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                data-testid={`button-review-${segment.id}`}
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                              <DialogHeader>
                                <DialogTitle>Review bản dịch - Segment {segment.segmentIndex}</DialogTitle>
                                <DialogDescription>
                                  Sử dụng AI để review và cải thiện bản dịch hiện tại với prompt tùy chỉnh và context xung quanh
                                </DialogDescription>
                              </DialogHeader>
                              
                              <div className="space-y-6">
                                {/* Original vs Current Translation */}
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <Label className="text-sm font-medium">Văn bản gốc</Label>
                                    <div className="mt-1 p-3 bg-muted rounded-md text-sm">
                                      {segment.originalText}
                                    </div>
                                  </div>
                                  <div>
                                    <Label className="text-sm font-medium">Bản dịch hiện tại</Label>
                                    <div className="mt-1 p-3 bg-accent/10 rounded-md text-sm">
                                      {segment.translatedText}
                                    </div>
                                  </div>
                                </div>

                                {/* Review Form */}
                                <div className="space-y-4">
                                  <div>
                                    <Label htmlFor="reviewPrompt">Review Prompt</Label>
                                    <Textarea
                                      id="reviewPrompt"
                                      value={reviewForm.reviewPrompt}
                                      onChange={(e) => setReviewForm(prev => ({ ...prev, reviewPrompt: e.target.value }))}
                                      placeholder="Nhập prompt để hướng dẫn AI review bản dịch..."
                                      className="mt-1 min-h-[80px]"
                                      data-testid="textarea-review-prompt"
                                    />
                                  </div>

                                  <div className="grid grid-cols-3 gap-4">
                                    <div>
                                      <Label htmlFor="contextWindow">Context Window: {reviewForm.contextWindow}</Label>
                                      <Slider
                                        id="contextWindow"
                                        value={[reviewForm.contextWindow!]}
                                        onValueChange={(value) => setReviewForm(prev => ({ ...prev, contextWindow: value[0] }))}
                                        max={5}
                                        min={0}
                                        step={1}
                                        className="mt-2"
                                        data-testid="slider-context-window"
                                      />
                                    </div>
                                    <div>
                                      <Label htmlFor="temperature">Temperature: {reviewForm.temperature}</Label>
                                      <Slider
                                        id="temperature"
                                        value={[reviewForm.temperature!]}
                                        onValueChange={(value) => setReviewForm(prev => ({ ...prev, temperature: value[0] }))}
                                        max={1}
                                        min={0}
                                        step={0.1}
                                        className="mt-2"
                                        data-testid="slider-temperature"
                                      />
                                    </div>
                                    <div>
                                      <Label htmlFor="maxTokens">Max Tokens</Label>
                                      <Input
                                        id="maxTokens"
                                        type="number"
                                        value={reviewForm.maxTokens}
                                        onChange={(e) => setReviewForm(prev => ({ ...prev, maxTokens: parseInt(e.target.value) || 500 }))}
                                        min={100}
                                        max={2000}
                                        className="mt-1"
                                        data-testid="input-max-tokens"
                                      />
                                    </div>
                                  </div>
                                </div>

                                {/* Review Result */}
                                {reviewResult && (
                                  <div className="space-y-4">
                                    <div>
                                      <Label className="text-sm font-medium">Bản dịch đã review</Label>
                                      <div className="mt-1 p-3 bg-primary/10 border-l-4 border-primary rounded-r text-sm">
                                        {reviewResult}
                                      </div>
                                    </div>
                                    
                                    <div className="flex justify-end gap-2">
                                      <Button
                                        variant="outline"
                                        onClick={cancelReview}
                                        data-testid={`button-discard-review-${segment.id}`}
                                      >
                                        <ThumbsDown className="h-4 w-4 mr-2" />
                                        Bỏ qua
                                      </Button>
                                      <Button
                                        onClick={applyReview}
                                        disabled={acceptReviewMutation.isPending}
                                        data-testid={`button-apply-review-${segment.id}`}
                                      >
                                        {acceptReviewMutation.isPending ? (
                                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                        ) : (
                                          <ThumbsUp className="h-4 w-4 mr-2" />
                                        )}
                                        Áp dụng
                                      </Button>
                                    </div>
                                  </div>
                                )}

                                {/* Actions */}
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="outline"
                                    onClick={cancelReview}
                                  >
                                    Hủy
                                  </Button>
                                  <Button
                                    onClick={submitReview}
                                    disabled={reviewTranslateMutation.isPending || !reviewForm.reviewPrompt.trim()}
                                    data-testid={`button-submit-review-${segment.id}`}
                                  >
                                    {reviewTranslateMutation.isPending ? (
                                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    ) : (
                                      <Eye className="h-4 w-4 mr-2" />
                                    )}
                                    Tạo Review
                                  </Button>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {segment.status === 'translating' && (
                  <div className="bg-primary/10 border-l-4 border-primary pl-3 py-1 rounded-r flex items-center">
                    <Loader2 className="h-4 w-4 animate-spin text-primary mr-2" />
                    <span className="text-primary font-medium text-xs">Đang dịch...</span>
                  </div>
                )}

                {segment.status === 'error' && (
                  <div className="bg-destructive/10 border-l-4 border-destructive pl-3 py-1 rounded-r">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center">
                        <AlertTriangle className="h-4 w-4 text-destructive mr-2" />
                        <span className="text-destructive font-medium text-xs">Lỗi dịch</span>
                        {segment.retryCount > 0 && (
                          <span className="text-orange-500 text-xs ml-2">
                            (Thử lại: {segment.retryCount}/{segment.maxRetries})
                          </span>
                        )}
                      </div>
                      {segment.retryCount < segment.maxRetries ? (
                        <span className="text-blue-500 text-xs font-medium">
                          Sẽ tự động thử lại
                        </span>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => retryMutation.mutate(segment.id)}
                          disabled={retryMutation.isPending}
                          className="h-6 px-2 text-xs"
                          data-testid={`button-retry-${segment.id}`}
                        >
                          <RotateCcw className="h-3 w-3 mr-1" />
                          Thử lại thủ công
                        </Button>
                      )}
                    </div>
                    {segment.lastError && (
                      <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded text-wrap break-words">
                        <strong>Chi tiết lỗi:</strong> {segment.lastError}
                      </div>
                    )}
                  </div>
                )}

                {segment.status === 'pending' && (
                  <div className="bg-muted/50 border-l-4 border-muted pl-3 py-1 rounded-r">
                    <span className="text-muted-foreground italic">Chưa dịch</span>
                  </div>
                )}

                {segment.status === 'paused' && (
                  <div className="bg-warning/10 border-l-4 border-warning pl-3 py-1 rounded-r">
                    <span className="text-warning font-medium text-xs">Tạm dừng</span>
                  </div>
                )}
              </div>

              {/* Status */}
              <div className="col-span-1 text-center">
                <div className={`status-indicator status-${segment.status}`} data-testid={`indicator-segment-status-${segment.id}`} />
                <div className={`text-xs font-medium mt-1 ${
                  segment.status === 'completed' ? 'text-success' :
                  segment.status === 'translating' ? 'text-primary' :
                  segment.status === 'error' ? 'text-destructive' :
                  segment.status === 'paused' ? 'text-warning' :
                  'text-muted-foreground'
                }`} data-testid={`text-status-${segment.id}`}>
                  {segment.status === 'completed' ? 'Hoàn thành' :
                   segment.status === 'translating' ? 'Đang dịch' :
                   segment.status === 'error' ? (
                     <div className="text-center">
                       <div>Lỗi</div>
                       {segment.retryCount > 0 && (
                         <div className="text-orange-500 text-xs">
                           Thử lại: {segment.retryCount}/{segment.maxRetries}
                         </div>
                       )}
                       {segment.retryCount < segment.maxRetries && (
                         <div className="text-blue-500 text-xs">
                           Sẽ thử lại
                         </div>
                       )}
                     </div>
                   ) :
                   segment.status === 'paused' ? 'Tạm dừng' :
                   'Chờ dịch'}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
