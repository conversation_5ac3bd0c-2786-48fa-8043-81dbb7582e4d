import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Pause, OctagonMinus, Play, Download, Eye, Trash2 } from "lucide-react";
import type { SubtitleFile } from "@shared/schema";

interface FilesListProps {
  files: SubtitleFile[];
  selectedFileId: string | null;
  onSelectFile: (fileId: string) => void;
  isLoading: boolean;
}

export function FilesList({ files, selectedFileId, onSelectFile, isLoading }: FilesListProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Control mutations
  const pauseMutation = useMutation({
    mutationFn: async (fileId: string) => {
      const response = await apiRequest("POST", `/api/translation/${fileId}/pause`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/files"] });
    },
  });

  const stopMutation = useMutation({
    mutationFn: async (fileId: string) => {
      const response = await apiRequest("POST", `/api/translation/${fileId}/stop`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/files"] });
    },
  });

  const resumeMutation = useMutation({
    mutationFn: async (fileId: string) => {
      const response = await apiRequest("POST", `/api/translation/${fileId}/resume`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/files"] });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async (fileId: string) => {
      const response = await apiRequest("DELETE", `/api/files/${fileId}`);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Thành công",
        description: "Đã xóa file",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/files"] });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Download mutation
  const downloadMutation = useMutation({
    mutationFn: async (fileId: string) => {
      const response = await apiRequest("GET", `/api/files/${fileId}/export`);
      const blob = new Blob([await response.text()], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      
      const file = files.find(f => f.id === fileId);
      const fileName = file?.fileName.replace('.srt', '_vietnamese.srt') || 'translated.srt';
      
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    },
    onSuccess: () => {
      toast({
        title: "Thành công",
        description: "Đã tải xuống file dịch",
      });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  if (isLoading) {
    return (
      <div className="flex-1 overflow-y-auto p-4">
        <h3 className="text-sm font-semibold text-foreground mb-3">Files đang xử lý</h3>
        <div className="text-center text-muted-foreground py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          Đang tải...
        </div>
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="flex-1 overflow-y-auto p-4">
        <h3 className="text-sm font-semibold text-foreground mb-3">Files đang xử lý</h3>
        <div className="text-center text-muted-foreground py-8">
          <p className="text-sm">Chưa có file nào được tải lên</p>
          <p className="text-xs mt-1">Kéo thả file SRT để bắt đầu</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="p-4">
        <h3 className="text-sm font-semibold text-foreground mb-3">Files đang xử lý</h3>
        
        {files.map((file) => {
          const progress = file.totalSegments > 0 ? Math.round((file.completedSegments / file.totalSegments) * 100) : 0;
          const isSelected = file.id === selectedFileId;
          
          return (
            <div 
              key={file.id} 
              className={`mb-3 bg-card border rounded-lg p-3 shadow-sm cursor-pointer transition-colors ${
                isSelected ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50'
              }`}
              onClick={() => onSelectFile(file.id)}
              data-testid={`card-file-${file.id}`}
            >
              <div className="flex items-center justify-between mb-2">
                <span 
                  className="text-sm font-medium text-foreground truncate flex-1" 
                  title={file.fileName}
                  data-testid={`text-filename-${file.id}`}
                >
                  {file.fileName}
                </span>
                <div className="flex items-center ml-2">
                  <div className={`status-indicator status-${file.status}`} data-testid={`indicator-status-${file.id}`} />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteMutation.mutate(file.id);
                    }}
                    disabled={deleteMutation.isPending}
                    className="p-1 h-6 w-6 ml-1"
                    data-testid={`button-delete-${file.id}`}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              
              <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
                <span data-testid={`text-progress-${file.id}`}>
                  {file.completedSegments}/{file.totalSegments} đoạn
                </span>
                <span data-testid={`text-percentage-${file.id}`}>
                  {file.status === 'completed' ? 'Hoàn thành' : `${progress}%`}
                </span>
              </div>
              
              <Progress 
                value={progress} 
                className="h-2 mb-2" 
                data-testid={`progress-${file.id}`}
              />
              
              <div className="flex gap-1">
                {file.status === 'translating' && (
                  <Button 
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      pauseMutation.mutate(file.id);
                    }}
                    disabled={pauseMutation.isPending}
                    className="flex-1 text-xs h-7"
                    data-testid={`button-pause-${file.id}`}
                  >
                    <Pause className="h-3 w-3 mr-1" />
                    Tạm dừng
                  </Button>
                )}
                
                {file.status === 'paused' && (
                  <Button 
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      resumeMutation.mutate(file.id);
                    }}
                    disabled={resumeMutation.isPending}
                    className="flex-1 text-xs h-7"
                    data-testid={`button-resume-${file.id}`}
                  >
                    <Play className="h-3 w-3 mr-1" />
                    Tiếp tục
                  </Button>
                )}
                
                {(file.status === 'translating' || file.status === 'paused') && (
                  <Button 
                    variant="destructive"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      stopMutation.mutate(file.id);
                    }}
                    disabled={stopMutation.isPending}
                    className="flex-1 text-xs h-7"
                    data-testid={`button-stop-${file.id}`}
                  >
                    <OctagonMinus className="h-3 w-3 mr-1" />
                    Dừng
                  </Button>
                )}
                
                {file.status === 'completed' && (
                  <>
                    <Button 
                      variant="secondary"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        downloadMutation.mutate(file.id);
                      }}
                      disabled={downloadMutation.isPending}
                      className="flex-1 text-xs h-7"
                      data-testid={`button-download-${file.id}`}
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Tải xuống
                    </Button>
                    <Button 
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onSelectFile(file.id);
                      }}
                      className="flex-1 text-xs h-7"
                      data-testid={`button-view-${file.id}`}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Xem
                    </Button>
                  </>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
