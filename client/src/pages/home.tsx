import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { FileUpload } from "@/components/FileUpload";
import { ConnectionStatus } from "@/components/ConnectionStatus";
import { FilesList } from "@/components/FilesList";
import { TranslationTable } from "@/components/TranslationTable";
import { StatusFooter } from "@/components/StatusFooter";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Languages, Play, FolderOutput } from "lucide-react";
import type { SubtitleFile, SubtitleSegment } from "@shared/schema";

export default function Home() {
  const [selectedFileId, setSelectedFileId] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch files
  const { data: files = [], isLoading: filesLoading } = useQuery<SubtitleFile[]>({
    queryKey: ["/api/files"],
    refetchInterval: 2000, // Refresh every 2 seconds for real-time updates
  });

  // Fetch segments for selected file
  const { data: segments = [], isLoading: segmentsLoading } = useQuery<SubtitleSegment[]>({
    queryKey: ["/api/files", selectedFileId, "segments"],
    enabled: !!selectedFileId,
    refetchInterval: 1000, // Refresh every second for real-time updates
  });

  // Select first file if none selected
  useEffect(() => {
    if (files.length > 0 && !selectedFileId) {
      setSelectedFileId(files[0].id);
    }
  }, [files, selectedFileId]);

  // Start translation mutation
  const startTranslationMutation = useMutation({
    mutationFn: async (fileId: string) => {
      const response = await apiRequest("POST", `/api/translation/${fileId}/start`);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Thành công",
        description: "Đã bắt đầu quá trình dịch",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/files"] });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Export all mutation
  const exportAllMutation = useMutation({
    mutationFn: async () => {
      const completedFiles = files.filter(f => f.status === 'completed');
      const downloads = await Promise.all(
        completedFiles.map(async (file) => {
          const response = await apiRequest("GET", `/api/files/${file.id}/export`);
          const blob = new Blob([await response.text()], { type: 'text/plain' });
          const url = URL.createObjectURL(blob);
          const fileName = file.fileName.replace('.srt', '_vietnamese.srt');
          return { url, fileName };
        })
      );
      return downloads;
    },
    onSuccess: (downloads) => {
      downloads.forEach(({ url, fileName }) => {
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      });
      toast({
        title: "Thành công",
        description: `Đã xuất ${downloads.length} file dịch`,
      });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const selectedFile = selectedFileId ? files.find(f => f.id === selectedFileId) : null;
  const completedFiles = files.filter(f => f.status === 'completed');

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar */}
      <div className="w-80 bg-card border-r border-border flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-2xl font-bold text-foreground flex items-center">
              <Languages className="text-primary mr-3" />
              SRT Translator
            </h1>

          </div>
          <p className="text-muted-foreground text-sm">Dịch phụ đề AI với LM Studio</p>
        </div>
        
        {/* Connection Status */}
        <ConnectionStatus />
        
        {/* File Upload */}
        <FileUpload onFileUploaded={() => {
          queryClient.invalidateQueries({ queryKey: ["/api/files"] });
        }} />
        
        {/* Files List */}
        <FilesList 
          files={files} 
          selectedFileId={selectedFileId}
          onSelectFile={setSelectedFileId}
          isLoading={filesLoading}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Toolbar */}
        <div className="bg-card border-b border-border px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-semibold text-foreground">
                {selectedFile?.fileName || "Chọn file để xem"}
              </h2>
              {selectedFile && (
                <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                  selectedFile.status === 'completed' ? 'bg-success/10 text-success' :
                  selectedFile.status === 'translating' ? 'bg-primary/10 text-primary' :
                  selectedFile.status === 'error' ? 'bg-destructive/10 text-destructive' :
                  selectedFile.status === 'paused' ? 'bg-warning/10 text-warning' :
                  'bg-muted/10 text-muted-foreground'
                }`}>
                  {selectedFile.status === 'completed' ? 'Hoàn thành' :
                   selectedFile.status === 'translating' ? 'Đang dịch' :
                   selectedFile.status === 'error' ? 'Lỗi' :
                   selectedFile.status === 'paused' ? 'Tạm dừng' :
                   'Chờ dịch'}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button 
                onClick={() => selectedFileId && startTranslationMutation.mutate(selectedFileId)}
                disabled={!selectedFileId || selectedFile?.status === 'translating' || startTranslationMutation.isPending}
                className="text-sm font-medium"
                data-testid="button-start-translation"
              >
                <Play className="mr-2 h-4 w-4" />
                Bắt đầu dịch
              </Button>
              <Button 
                variant="secondary"
                onClick={() => exportAllMutation.mutate()}
                disabled={completedFiles.length === 0 || exportAllMutation.isPending}
                className="text-sm font-medium"
                data-testid="button-export-all"
              >
                <FolderOutput className="mr-2 h-4 w-4" />
                Xuất tất cả ({completedFiles.length})
              </Button>
            </div>
          </div>
        </div>

        {/* Translation Table */}
        <TranslationTable 
          segments={segments}
          isLoading={segmentsLoading}
          selectedFile={selectedFile || null}
        />

        {/* Status Footer */}
        <StatusFooter 
          segments={segments}
          files={files}
        />
      </div>
    </div>
  );
}
