import { sql, relations } from "drizzle-orm";
import { pgTable, text, varchar, timestamp, integer, boolean, real } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const subtitleFiles = pgTable("subtitle_files", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  fileName: text("file_name").notNull(),
  originalContent: text("original_content").notNull(),
  status: text("status", { enum: ["pending", "translating", "completed", "paused", "error"] }).notNull().default("pending"),
  totalSegments: integer("total_segments").notNull().default(0),
  completedSegments: integer("completed_segments").notNull().default(0),
  errorSegments: integer("error_segments").notNull().default(0),
  createdAt: timestamp("created_at").notNull().default(sql`now()`),
  updatedAt: timestamp("updated_at").notNull().default(sql`now()`),
});

export const subtitleSegments = pgTable("subtitle_segments", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  fileId: varchar("file_id").notNull().references(() => subtitleFiles.id, { onDelete: "cascade" }),
  segmentIndex: integer("segment_index").notNull(),
  startTime: text("start_time").notNull(),
  endTime: text("end_time").notNull(),
  originalText: text("original_text").notNull(),
  translatedText: text("translated_text"),
  status: text("status", { enum: ["pending", "translating", "completed", "error", "paused"] }).notNull().default("pending"),
  context: text("context"), // Previous and next segments for better translation
  
  // Retry mechanism fields
  retryCount: integer("retry_count").notNull().default(0),
  maxRetries: integer("max_retries").notNull().default(3),
  lastError: text("last_error"),
  nextRetryAt: timestamp("next_retry_at"),
  
  // Review translation fields
  reviewTranslatedText: text("review_translated_text"),
  reviewPrompt: text("review_prompt"),
  reviewedAt: timestamp("reviewed_at"),
  
  createdAt: timestamp("created_at").notNull().default(sql`now()`),
  updatedAt: timestamp("updated_at").notNull().default(sql`now()`),
});

export const translationSettings = pgTable("translation_settings", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  lmStudioUrl: text("lm_studio_url").notNull().default("http://localhost:1234"),
  selectedModel: text("selected_model"),
  isConnected: boolean("is_connected").notNull().default(false),
  lastChecked: timestamp("last_checked"),
  
  // Batch translation settings
  batchSize: integer("batch_size").notNull().default(1),
  delayBetweenBatches: integer("delay_between_batches").notNull().default(100), // milliseconds
  contextWindow: integer("context_window").notNull().default(2), // how many segments before/after for context
  
  // LM Studio API parameters
  temperature: real("temperature").notNull().default(0.3),
  maxTokens: integer("max_tokens").notNull().default(500),
  
  // Custom prompts
  systemPrompt: text("system_prompt").notNull().default("You are a professional Japanese to Vietnamese translator. Translate the given Japanese subtitle text to natural Vietnamese. Keep the same tone and meaning. Only return the translated text without explanations."),
  contextPrompt: text("context_prompt").notNull().default("Context: {context}"),
  
  createdAt: timestamp("created_at").notNull().default(sql`now()`),
  updatedAt: timestamp("updated_at").notNull().default(sql`now()`),
});

export const subtitleFilesRelations = relations(subtitleFiles, ({ many }) => ({
  segments: many(subtitleSegments),
}));

export const subtitleSegmentsRelations = relations(subtitleSegments, ({ one }) => ({
  file: one(subtitleFiles, {
    fields: [subtitleSegments.fileId],
    references: [subtitleFiles.id],
  }),
}));

export const insertSubtitleFileSchema = createInsertSchema(subtitleFiles).pick({
  fileName: true,
  originalContent: true,
});

export const insertSubtitleSegmentSchema = createInsertSchema(subtitleSegments).pick({
  fileId: true,
  segmentIndex: true,
  startTime: true,
  endTime: true,
  originalText: true,
  context: true,
});

export const insertTranslationSettingsSchema = createInsertSchema(translationSettings).pick({
  lmStudioUrl: true,
  selectedModel: true,
  batchSize: true,
  delayBetweenBatches: true,
  contextWindow: true,
  temperature: true,
  maxTokens: true,
  systemPrompt: true,
  contextPrompt: true,
}).extend({
  batchSize: z.number().int().min(1).max(10), // reasonable limits
  delayBetweenBatches: z.number().int().min(0).max(5000), // 0-5 seconds
  contextWindow: z.number().int().min(0).max(5), // 0-5 segments
  temperature: z.number().min(0).max(1), // 0-1 for LLM temperature
  maxTokens: z.number().int().min(50).max(2000), // reasonable token limits
  systemPrompt: z.string().min(10).max(1000), // reasonable prompt length
  contextPrompt: z.string().min(1).max(200), // shorter context template
});

export type SubtitleFile = typeof subtitleFiles.$inferSelect;
export type SubtitleSegment = typeof subtitleSegments.$inferSelect;
export type TranslationSettings = typeof translationSettings.$inferSelect;
export type InsertSubtitleFile = z.infer<typeof insertSubtitleFileSchema>;
export type InsertSubtitleSegment = z.infer<typeof insertSubtitleSegmentSchema>;
export type InsertTranslationSettings = z.infer<typeof insertTranslationSettingsSchema>;

// Review translation schema
export const reviewTranslateSchema = z.object({
  reviewPrompt: z.string().min(10).max(1000),
  contextWindow: z.number().int().min(0).max(5).optional(),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().int().min(100).max(2000).optional(),
});

export type ReviewTranslateRequest = z.infer<typeof reviewTranslateSchema>;

// Update translation settings schema (partial update)
export const updateTranslationSettingsSchema = z.object({
  selectedModel: z.string().optional(),
  lmStudioUrl: z.string().optional(),
  batchSize: z.number().int().min(1).max(10).optional(),
  delayBetweenBatches: z.number().int().min(0).max(5000).optional(),
  contextWindow: z.number().int().min(0).max(5).optional(),
  temperature: z.number().min(0).max(1).optional(),
  maxTokens: z.number().int().min(50).max(2000).optional(),
  systemPrompt: z.string().min(10).max(1000).optional(),
  contextPrompt: z.string().min(1).max(200).optional(),
});

export type UpdateTranslationSettings = z.infer<typeof updateTranslationSettingsSchema>;
